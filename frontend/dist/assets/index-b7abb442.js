var Ju=Object.defineProperty;var ed=(e,t,n)=>t in e?Ju(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;var Me=(e,t,n)=>(ed(e,typeof t!="symbol"?t+"":t,n),n);(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const l of document.querySelectorAll('link[rel="modulepreload"]'))r(l);new MutationObserver(l=>{for(const i of l)if(i.type==="childList")for(const o of i.addedNodes)o.tagName==="LINK"&&o.rel==="modulepreload"&&r(o)}).observe(document,{childList:!0,subtree:!0});function n(l){const i={};return l.integrity&&(i.integrity=l.integrity),l.referrerPolicy&&(i.referrerPolicy=l.referrerPolicy),l.crossOrigin==="use-credentials"?i.credentials="include":l.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function r(l){if(l.ep)return;l.ep=!0;const i=n(l);fetch(l.href,i)}})();function td(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var La={exports:{}},Cs={},Ta={exports:{}},$={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var xr=Symbol.for("react.element"),nd=Symbol.for("react.portal"),rd=Symbol.for("react.fragment"),sd=Symbol.for("react.strict_mode"),ld=Symbol.for("react.profiler"),id=Symbol.for("react.provider"),od=Symbol.for("react.context"),ad=Symbol.for("react.forward_ref"),cd=Symbol.for("react.suspense"),ud=Symbol.for("react.memo"),dd=Symbol.for("react.lazy"),mo=Symbol.iterator;function md(e){return e===null||typeof e!="object"?null:(e=mo&&e[mo]||e["@@iterator"],typeof e=="function"?e:null)}var Da={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},Ma=Object.assign,Aa={};function Ln(e,t,n){this.props=e,this.context=t,this.refs=Aa,this.updater=n||Da}Ln.prototype.isReactComponent={};Ln.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};Ln.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function Ia(){}Ia.prototype=Ln.prototype;function gi(e,t,n){this.props=e,this.context=t,this.refs=Aa,this.updater=n||Da}var xi=gi.prototype=new Ia;xi.constructor=gi;Ma(xi,Ln.prototype);xi.isPureReactComponent=!0;var fo=Array.isArray,_a=Object.prototype.hasOwnProperty,yi={current:null},Pa={key:!0,ref:!0,__self:!0,__source:!0};function za(e,t,n){var r,l={},i=null,o=null;if(t!=null)for(r in t.ref!==void 0&&(o=t.ref),t.key!==void 0&&(i=""+t.key),t)_a.call(t,r)&&!Pa.hasOwnProperty(r)&&(l[r]=t[r]);var a=arguments.length-2;if(a===1)l.children=n;else if(1<a){for(var c=Array(a),f=0;f<a;f++)c[f]=arguments[f+2];l.children=c}if(e&&e.defaultProps)for(r in a=e.defaultProps,a)l[r]===void 0&&(l[r]=a[r]);return{$$typeof:xr,type:e,key:i,ref:o,props:l,_owner:yi.current}}function fd(e,t){return{$$typeof:xr,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function vi(e){return typeof e=="object"&&e!==null&&e.$$typeof===xr}function pd(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var po=/\/+/g;function Vs(e,t){return typeof e=="object"&&e!==null&&e.key!=null?pd(""+e.key):t.toString(36)}function Ur(e,t,n,r,l){var i=typeof e;(i==="undefined"||i==="boolean")&&(e=null);var o=!1;if(e===null)o=!0;else switch(i){case"string":case"number":o=!0;break;case"object":switch(e.$$typeof){case xr:case nd:o=!0}}if(o)return o=e,l=l(o),e=r===""?"."+Vs(o,0):r,fo(l)?(n="",e!=null&&(n=e.replace(po,"$&/")+"/"),Ur(l,t,n,"",function(f){return f})):l!=null&&(vi(l)&&(l=fd(l,n+(!l.key||o&&o.key===l.key?"":(""+l.key).replace(po,"$&/")+"/")+e)),t.push(l)),1;if(o=0,r=r===""?".":r+":",fo(e))for(var a=0;a<e.length;a++){i=e[a];var c=r+Vs(i,a);o+=Ur(i,t,n,c,l)}else if(c=md(e),typeof c=="function")for(e=c.call(e),a=0;!(i=e.next()).done;)i=i.value,c=r+Vs(i,a++),o+=Ur(i,t,n,c,l);else if(i==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return o}function Nr(e,t,n){if(e==null)return e;var r=[],l=0;return Ur(e,r,"","",function(i){return t.call(n,i,l++)}),r}function hd(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var xe={current:null},Fr={transition:null},gd={ReactCurrentDispatcher:xe,ReactCurrentBatchConfig:Fr,ReactCurrentOwner:yi};function Ra(){throw Error("act(...) is not supported in production builds of React.")}$.Children={map:Nr,forEach:function(e,t,n){Nr(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return Nr(e,function(){t++}),t},toArray:function(e){return Nr(e,function(t){return t})||[]},only:function(e){if(!vi(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};$.Component=Ln;$.Fragment=rd;$.Profiler=ld;$.PureComponent=gi;$.StrictMode=sd;$.Suspense=cd;$.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=gd;$.act=Ra;$.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=Ma({},e.props),l=e.key,i=e.ref,o=e._owner;if(t!=null){if(t.ref!==void 0&&(i=t.ref,o=yi.current),t.key!==void 0&&(l=""+t.key),e.type&&e.type.defaultProps)var a=e.type.defaultProps;for(c in t)_a.call(t,c)&&!Pa.hasOwnProperty(c)&&(r[c]=t[c]===void 0&&a!==void 0?a[c]:t[c])}var c=arguments.length-2;if(c===1)r.children=n;else if(1<c){a=Array(c);for(var f=0;f<c;f++)a[f]=arguments[f+2];r.children=a}return{$$typeof:xr,type:e.type,key:l,ref:i,props:r,_owner:o}};$.createContext=function(e){return e={$$typeof:od,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:id,_context:e},e.Consumer=e};$.createElement=za;$.createFactory=function(e){var t=za.bind(null,e);return t.type=e,t};$.createRef=function(){return{current:null}};$.forwardRef=function(e){return{$$typeof:ad,render:e}};$.isValidElement=vi;$.lazy=function(e){return{$$typeof:dd,_payload:{_status:-1,_result:e},_init:hd}};$.memo=function(e,t){return{$$typeof:ud,type:e,compare:t===void 0?null:t}};$.startTransition=function(e){var t=Fr.transition;Fr.transition={};try{e()}finally{Fr.transition=t}};$.unstable_act=Ra;$.useCallback=function(e,t){return xe.current.useCallback(e,t)};$.useContext=function(e){return xe.current.useContext(e)};$.useDebugValue=function(){};$.useDeferredValue=function(e){return xe.current.useDeferredValue(e)};$.useEffect=function(e,t){return xe.current.useEffect(e,t)};$.useId=function(){return xe.current.useId()};$.useImperativeHandle=function(e,t,n){return xe.current.useImperativeHandle(e,t,n)};$.useInsertionEffect=function(e,t){return xe.current.useInsertionEffect(e,t)};$.useLayoutEffect=function(e,t){return xe.current.useLayoutEffect(e,t)};$.useMemo=function(e,t){return xe.current.useMemo(e,t)};$.useReducer=function(e,t,n){return xe.current.useReducer(e,t,n)};$.useRef=function(e){return xe.current.useRef(e)};$.useState=function(e){return xe.current.useState(e)};$.useSyncExternalStore=function(e,t,n){return xe.current.useSyncExternalStore(e,t,n)};$.useTransition=function(){return xe.current.useTransition()};$.version="18.3.1";Ta.exports=$;var L=Ta.exports;const $a=td(L);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var xd=L,yd=Symbol.for("react.element"),vd=Symbol.for("react.fragment"),jd=Object.prototype.hasOwnProperty,wd=xd.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,Nd={key:!0,ref:!0,__self:!0,__source:!0};function Oa(e,t,n){var r,l={},i=null,o=null;n!==void 0&&(i=""+n),t.key!==void 0&&(i=""+t.key),t.ref!==void 0&&(o=t.ref);for(r in t)jd.call(t,r)&&!Nd.hasOwnProperty(r)&&(l[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)l[r]===void 0&&(l[r]=t[r]);return{$$typeof:yd,type:e,key:i,ref:o,props:l,_owner:wd.current}}Cs.Fragment=vd;Cs.jsx=Oa;Cs.jsxs=Oa;La.exports=Cs;var s=La.exports,gl={},Ua={exports:{}},Te={},Fa={exports:{}},Va={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(b,D){var C=b.length;b.push(D);e:for(;0<C;){var O=C-1>>>1,R=b[O];if(0<l(R,D))b[O]=D,b[C]=R,C=O;else break e}}function n(b){return b.length===0?null:b[0]}function r(b){if(b.length===0)return null;var D=b[0],C=b.pop();if(C!==D){b[0]=C;e:for(var O=0,R=b.length,zt=R>>>1;O<zt;){var ee=2*(O+1)-1,Qe=b[ee],Q=ee+1,Je=b[Q];if(0>l(Qe,C))Q<R&&0>l(Je,Qe)?(b[O]=Je,b[Q]=C,O=Q):(b[O]=Qe,b[ee]=C,O=ee);else if(Q<R&&0>l(Je,C))b[O]=Je,b[Q]=C,O=Q;else break e}}return D}function l(b,D){var C=b.sortIndex-D.sortIndex;return C!==0?C:b.id-D.id}if(typeof performance=="object"&&typeof performance.now=="function"){var i=performance;e.unstable_now=function(){return i.now()}}else{var o=Date,a=o.now();e.unstable_now=function(){return o.now()-a}}var c=[],f=[],x=1,g=null,y=3,v=!1,w=!1,h=!1,N=typeof setTimeout=="function"?setTimeout:null,m=typeof clearTimeout=="function"?clearTimeout:null,d=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function u(b){for(var D=n(f);D!==null;){if(D.callback===null)r(f);else if(D.startTime<=b)r(f),D.sortIndex=D.expirationTime,t(c,D);else break;D=n(f)}}function p(b){if(h=!1,u(b),!w)if(n(c)!==null)w=!0,I(j);else{var D=n(f);D!==null&&z(p,D.startTime-b)}}function j(b,D){w=!1,h&&(h=!1,m(A),A=-1),v=!0;var C=y;try{for(u(D),g=n(c);g!==null&&(!(g.expirationTime>D)||b&&!ve());){var O=g.callback;if(typeof O=="function"){g.callback=null,y=g.priorityLevel;var R=O(g.expirationTime<=D);D=e.unstable_now(),typeof R=="function"?g.callback=R:g===n(c)&&r(c),u(D)}else r(c);g=n(c)}if(g!==null)var zt=!0;else{var ee=n(f);ee!==null&&z(p,ee.startTime-D),zt=!1}return zt}finally{g=null,y=C,v=!1}}var S=!1,T=null,A=-1,V=5,_=-1;function ve(){return!(e.unstable_now()-_<V)}function Ze(){if(T!==null){var b=e.unstable_now();_=b;var D=!0;try{D=T(!0,b)}finally{D?dt():(S=!1,T=null)}}else S=!1}var dt;if(typeof d=="function")dt=function(){d(Ze)};else if(typeof MessageChannel<"u"){var Mn=new MessageChannel,M=Mn.port2;Mn.port1.onmessage=Ze,dt=function(){M.postMessage(null)}}else dt=function(){N(Ze,0)};function I(b){T=b,S||(S=!0,dt())}function z(b,D){A=N(function(){b(e.unstable_now())},D)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(b){b.callback=null},e.unstable_continueExecution=function(){w||v||(w=!0,I(j))},e.unstable_forceFrameRate=function(b){0>b||125<b?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):V=0<b?Math.floor(1e3/b):5},e.unstable_getCurrentPriorityLevel=function(){return y},e.unstable_getFirstCallbackNode=function(){return n(c)},e.unstable_next=function(b){switch(y){case 1:case 2:case 3:var D=3;break;default:D=y}var C=y;y=D;try{return b()}finally{y=C}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(b,D){switch(b){case 1:case 2:case 3:case 4:case 5:break;default:b=3}var C=y;y=b;try{return D()}finally{y=C}},e.unstable_scheduleCallback=function(b,D,C){var O=e.unstable_now();switch(typeof C=="object"&&C!==null?(C=C.delay,C=typeof C=="number"&&0<C?O+C:O):C=O,b){case 1:var R=-1;break;case 2:R=250;break;case 5:R=**********;break;case 4:R=1e4;break;default:R=5e3}return R=C+R,b={id:x++,callback:D,priorityLevel:b,startTime:C,expirationTime:R,sortIndex:-1},C>O?(b.sortIndex=C,t(f,b),n(c)===null&&b===n(f)&&(h?(m(A),A=-1):h=!0,z(p,C-O))):(b.sortIndex=R,t(c,b),w||v||(w=!0,I(j))),b},e.unstable_shouldYield=ve,e.unstable_wrapCallback=function(b){var D=y;return function(){var C=y;y=D;try{return b.apply(this,arguments)}finally{y=C}}}})(Va);Fa.exports=Va;var Sd=Fa.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var kd=L,Le=Sd;function k(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var Ha=new Set,Jn={};function Xt(e,t){jn(e,t),jn(e+"Capture",t)}function jn(e,t){for(Jn[e]=t,e=0;e<t.length;e++)Ha.add(t[e])}var it=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),xl=Object.prototype.hasOwnProperty,bd=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,ho={},go={};function Cd(e){return xl.call(go,e)?!0:xl.call(ho,e)?!1:bd.test(e)?go[e]=!0:(ho[e]=!0,!1)}function Ed(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function Ld(e,t,n,r){if(t===null||typeof t>"u"||Ed(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function ye(e,t,n,r,l,i,o){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=l,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=i,this.removeEmptyString=o}var ue={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){ue[e]=new ye(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];ue[t]=new ye(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){ue[e]=new ye(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){ue[e]=new ye(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){ue[e]=new ye(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){ue[e]=new ye(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){ue[e]=new ye(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){ue[e]=new ye(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){ue[e]=new ye(e,5,!1,e.toLowerCase(),null,!1,!1)});var ji=/[\-:]([a-z])/g;function wi(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(ji,wi);ue[t]=new ye(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(ji,wi);ue[t]=new ye(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(ji,wi);ue[t]=new ye(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){ue[e]=new ye(e,1,!1,e.toLowerCase(),null,!1,!1)});ue.xlinkHref=new ye("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){ue[e]=new ye(e,1,!1,e.toLowerCase(),null,!0,!0)});function Ni(e,t,n,r){var l=ue.hasOwnProperty(t)?ue[t]:null;(l!==null?l.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(Ld(t,n,l,r)&&(n=null),r||l===null?Cd(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):l.mustUseProperty?e[l.propertyName]=n===null?l.type===3?!1:"":n:(t=l.attributeName,r=l.attributeNamespace,n===null?e.removeAttribute(t):(l=l.type,n=l===3||l===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var ut=kd.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,Sr=Symbol.for("react.element"),tn=Symbol.for("react.portal"),nn=Symbol.for("react.fragment"),Si=Symbol.for("react.strict_mode"),yl=Symbol.for("react.profiler"),Ba=Symbol.for("react.provider"),Wa=Symbol.for("react.context"),ki=Symbol.for("react.forward_ref"),vl=Symbol.for("react.suspense"),jl=Symbol.for("react.suspense_list"),bi=Symbol.for("react.memo"),ft=Symbol.for("react.lazy"),Qa=Symbol.for("react.offscreen"),xo=Symbol.iterator;function An(e){return e===null||typeof e!="object"?null:(e=xo&&e[xo]||e["@@iterator"],typeof e=="function"?e:null)}var Z=Object.assign,Hs;function Un(e){if(Hs===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);Hs=t&&t[1]||""}return`
`+Hs+e}var Bs=!1;function Ws(e,t){if(!e||Bs)return"";Bs=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(f){var r=f}Reflect.construct(e,[],t)}else{try{t.call()}catch(f){r=f}e.call(t.prototype)}else{try{throw Error()}catch(f){r=f}e()}}catch(f){if(f&&r&&typeof f.stack=="string"){for(var l=f.stack.split(`
`),i=r.stack.split(`
`),o=l.length-1,a=i.length-1;1<=o&&0<=a&&l[o]!==i[a];)a--;for(;1<=o&&0<=a;o--,a--)if(l[o]!==i[a]){if(o!==1||a!==1)do if(o--,a--,0>a||l[o]!==i[a]){var c=`
`+l[o].replace(" at new "," at ");return e.displayName&&c.includes("<anonymous>")&&(c=c.replace("<anonymous>",e.displayName)),c}while(1<=o&&0<=a);break}}}finally{Bs=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?Un(e):""}function Td(e){switch(e.tag){case 5:return Un(e.type);case 16:return Un("Lazy");case 13:return Un("Suspense");case 19:return Un("SuspenseList");case 0:case 2:case 15:return e=Ws(e.type,!1),e;case 11:return e=Ws(e.type.render,!1),e;case 1:return e=Ws(e.type,!0),e;default:return""}}function wl(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case nn:return"Fragment";case tn:return"Portal";case yl:return"Profiler";case Si:return"StrictMode";case vl:return"Suspense";case jl:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case Wa:return(e.displayName||"Context")+".Consumer";case Ba:return(e._context.displayName||"Context")+".Provider";case ki:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case bi:return t=e.displayName||null,t!==null?t:wl(e.type)||"Memo";case ft:t=e._payload,e=e._init;try{return wl(e(t))}catch{}}return null}function Dd(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return wl(t);case 8:return t===Si?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function Dt(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function Ka(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function Md(e){var t=Ka(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var l=n.get,i=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return l.call(this)},set:function(o){r=""+o,i.call(this,o)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(o){r=""+o},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function kr(e){e._valueTracker||(e._valueTracker=Md(e))}function Ga(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=Ka(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function Jr(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function Nl(e,t){var n=t.checked;return Z({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function yo(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=Dt(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function qa(e,t){t=t.checked,t!=null&&Ni(e,"checked",t,!1)}function Sl(e,t){qa(e,t);var n=Dt(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?kl(e,t.type,n):t.hasOwnProperty("defaultValue")&&kl(e,t.type,Dt(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function vo(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function kl(e,t,n){(t!=="number"||Jr(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var Fn=Array.isArray;function pn(e,t,n,r){if(e=e.options,t){t={};for(var l=0;l<n.length;l++)t["$"+n[l]]=!0;for(n=0;n<e.length;n++)l=t.hasOwnProperty("$"+e[n].value),e[n].selected!==l&&(e[n].selected=l),l&&r&&(e[n].defaultSelected=!0)}else{for(n=""+Dt(n),t=null,l=0;l<e.length;l++){if(e[l].value===n){e[l].selected=!0,r&&(e[l].defaultSelected=!0);return}t!==null||e[l].disabled||(t=e[l])}t!==null&&(t.selected=!0)}}function bl(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(k(91));return Z({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function jo(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(k(92));if(Fn(n)){if(1<n.length)throw Error(k(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:Dt(n)}}function Ya(e,t){var n=Dt(t.value),r=Dt(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function wo(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function Xa(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function Cl(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?Xa(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var br,Za=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,l){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,l)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(br=br||document.createElement("div"),br.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=br.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function er(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var Bn={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},Ad=["Webkit","ms","Moz","O"];Object.keys(Bn).forEach(function(e){Ad.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),Bn[t]=Bn[e]})});function Ja(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||Bn.hasOwnProperty(e)&&Bn[e]?(""+t).trim():t+"px"}function ec(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,l=Ja(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,l):e[n]=l}}var Id=Z({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function El(e,t){if(t){if(Id[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(k(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(k(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(k(61))}if(t.style!=null&&typeof t.style!="object")throw Error(k(62))}}function Ll(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Tl=null;function Ci(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Dl=null,hn=null,gn=null;function No(e){if(e=jr(e)){if(typeof Dl!="function")throw Error(k(280));var t=e.stateNode;t&&(t=Ms(t),Dl(e.stateNode,e.type,t))}}function tc(e){hn?gn?gn.push(e):gn=[e]:hn=e}function nc(){if(hn){var e=hn,t=gn;if(gn=hn=null,No(e),t)for(e=0;e<t.length;e++)No(t[e])}}function rc(e,t){return e(t)}function sc(){}var Qs=!1;function lc(e,t,n){if(Qs)return e(t,n);Qs=!0;try{return rc(e,t,n)}finally{Qs=!1,(hn!==null||gn!==null)&&(sc(),nc())}}function tr(e,t){var n=e.stateNode;if(n===null)return null;var r=Ms(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(k(231,t,typeof n));return n}var Ml=!1;if(it)try{var In={};Object.defineProperty(In,"passive",{get:function(){Ml=!0}}),window.addEventListener("test",In,In),window.removeEventListener("test",In,In)}catch{Ml=!1}function _d(e,t,n,r,l,i,o,a,c){var f=Array.prototype.slice.call(arguments,3);try{t.apply(n,f)}catch(x){this.onError(x)}}var Wn=!1,es=null,ts=!1,Al=null,Pd={onError:function(e){Wn=!0,es=e}};function zd(e,t,n,r,l,i,o,a,c){Wn=!1,es=null,_d.apply(Pd,arguments)}function Rd(e,t,n,r,l,i,o,a,c){if(zd.apply(this,arguments),Wn){if(Wn){var f=es;Wn=!1,es=null}else throw Error(k(198));ts||(ts=!0,Al=f)}}function Zt(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function ic(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function So(e){if(Zt(e)!==e)throw Error(k(188))}function $d(e){var t=e.alternate;if(!t){if(t=Zt(e),t===null)throw Error(k(188));return t!==e?null:e}for(var n=e,r=t;;){var l=n.return;if(l===null)break;var i=l.alternate;if(i===null){if(r=l.return,r!==null){n=r;continue}break}if(l.child===i.child){for(i=l.child;i;){if(i===n)return So(l),e;if(i===r)return So(l),t;i=i.sibling}throw Error(k(188))}if(n.return!==r.return)n=l,r=i;else{for(var o=!1,a=l.child;a;){if(a===n){o=!0,n=l,r=i;break}if(a===r){o=!0,r=l,n=i;break}a=a.sibling}if(!o){for(a=i.child;a;){if(a===n){o=!0,n=i,r=l;break}if(a===r){o=!0,r=i,n=l;break}a=a.sibling}if(!o)throw Error(k(189))}}if(n.alternate!==r)throw Error(k(190))}if(n.tag!==3)throw Error(k(188));return n.stateNode.current===n?e:t}function oc(e){return e=$d(e),e!==null?ac(e):null}function ac(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=ac(e);if(t!==null)return t;e=e.sibling}return null}var cc=Le.unstable_scheduleCallback,ko=Le.unstable_cancelCallback,Od=Le.unstable_shouldYield,Ud=Le.unstable_requestPaint,te=Le.unstable_now,Fd=Le.unstable_getCurrentPriorityLevel,Ei=Le.unstable_ImmediatePriority,uc=Le.unstable_UserBlockingPriority,ns=Le.unstable_NormalPriority,Vd=Le.unstable_LowPriority,dc=Le.unstable_IdlePriority,Es=null,Ye=null;function Hd(e){if(Ye&&typeof Ye.onCommitFiberRoot=="function")try{Ye.onCommitFiberRoot(Es,e,void 0,(e.current.flags&128)===128)}catch{}}var He=Math.clz32?Math.clz32:Qd,Bd=Math.log,Wd=Math.LN2;function Qd(e){return e>>>=0,e===0?32:31-(Bd(e)/Wd|0)|0}var Cr=64,Er=4194304;function Vn(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function rs(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,l=e.suspendedLanes,i=e.pingedLanes,o=n&268435455;if(o!==0){var a=o&~l;a!==0?r=Vn(a):(i&=o,i!==0&&(r=Vn(i)))}else o=n&~l,o!==0?r=Vn(o):i!==0&&(r=Vn(i));if(r===0)return 0;if(t!==0&&t!==r&&!(t&l)&&(l=r&-r,i=t&-t,l>=i||l===16&&(i&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-He(t),l=1<<n,r|=e[n],t&=~l;return r}function Kd(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Gd(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,l=e.expirationTimes,i=e.pendingLanes;0<i;){var o=31-He(i),a=1<<o,c=l[o];c===-1?(!(a&n)||a&r)&&(l[o]=Kd(a,t)):c<=t&&(e.expiredLanes|=a),i&=~a}}function Il(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function mc(){var e=Cr;return Cr<<=1,!(Cr&4194240)&&(Cr=64),e}function Ks(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function yr(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-He(t),e[t]=n}function qd(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var l=31-He(n),i=1<<l;t[l]=0,r[l]=-1,e[l]=-1,n&=~i}}function Li(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-He(n),l=1<<r;l&t|e[r]&t&&(e[r]|=t),n&=~l}}var H=0;function fc(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var pc,Ti,hc,gc,xc,_l=!1,Lr=[],jt=null,wt=null,Nt=null,nr=new Map,rr=new Map,ht=[],Yd="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function bo(e,t){switch(e){case"focusin":case"focusout":jt=null;break;case"dragenter":case"dragleave":wt=null;break;case"mouseover":case"mouseout":Nt=null;break;case"pointerover":case"pointerout":nr.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":rr.delete(t.pointerId)}}function _n(e,t,n,r,l,i){return e===null||e.nativeEvent!==i?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:i,targetContainers:[l]},t!==null&&(t=jr(t),t!==null&&Ti(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,l!==null&&t.indexOf(l)===-1&&t.push(l),e)}function Xd(e,t,n,r,l){switch(t){case"focusin":return jt=_n(jt,e,t,n,r,l),!0;case"dragenter":return wt=_n(wt,e,t,n,r,l),!0;case"mouseover":return Nt=_n(Nt,e,t,n,r,l),!0;case"pointerover":var i=l.pointerId;return nr.set(i,_n(nr.get(i)||null,e,t,n,r,l)),!0;case"gotpointercapture":return i=l.pointerId,rr.set(i,_n(rr.get(i)||null,e,t,n,r,l)),!0}return!1}function yc(e){var t=Ft(e.target);if(t!==null){var n=Zt(t);if(n!==null){if(t=n.tag,t===13){if(t=ic(n),t!==null){e.blockedOn=t,xc(e.priority,function(){hc(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Vr(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=Pl(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);Tl=r,n.target.dispatchEvent(r),Tl=null}else return t=jr(n),t!==null&&Ti(t),e.blockedOn=n,!1;t.shift()}return!0}function Co(e,t,n){Vr(e)&&n.delete(t)}function Zd(){_l=!1,jt!==null&&Vr(jt)&&(jt=null),wt!==null&&Vr(wt)&&(wt=null),Nt!==null&&Vr(Nt)&&(Nt=null),nr.forEach(Co),rr.forEach(Co)}function Pn(e,t){e.blockedOn===t&&(e.blockedOn=null,_l||(_l=!0,Le.unstable_scheduleCallback(Le.unstable_NormalPriority,Zd)))}function sr(e){function t(l){return Pn(l,e)}if(0<Lr.length){Pn(Lr[0],e);for(var n=1;n<Lr.length;n++){var r=Lr[n];r.blockedOn===e&&(r.blockedOn=null)}}for(jt!==null&&Pn(jt,e),wt!==null&&Pn(wt,e),Nt!==null&&Pn(Nt,e),nr.forEach(t),rr.forEach(t),n=0;n<ht.length;n++)r=ht[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<ht.length&&(n=ht[0],n.blockedOn===null);)yc(n),n.blockedOn===null&&ht.shift()}var xn=ut.ReactCurrentBatchConfig,ss=!0;function Jd(e,t,n,r){var l=H,i=xn.transition;xn.transition=null;try{H=1,Di(e,t,n,r)}finally{H=l,xn.transition=i}}function em(e,t,n,r){var l=H,i=xn.transition;xn.transition=null;try{H=4,Di(e,t,n,r)}finally{H=l,xn.transition=i}}function Di(e,t,n,r){if(ss){var l=Pl(e,t,n,r);if(l===null)rl(e,t,r,ls,n),bo(e,r);else if(Xd(l,e,t,n,r))r.stopPropagation();else if(bo(e,r),t&4&&-1<Yd.indexOf(e)){for(;l!==null;){var i=jr(l);if(i!==null&&pc(i),i=Pl(e,t,n,r),i===null&&rl(e,t,r,ls,n),i===l)break;l=i}l!==null&&r.stopPropagation()}else rl(e,t,r,null,n)}}var ls=null;function Pl(e,t,n,r){if(ls=null,e=Ci(r),e=Ft(e),e!==null)if(t=Zt(e),t===null)e=null;else if(n=t.tag,n===13){if(e=ic(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return ls=e,null}function vc(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Fd()){case Ei:return 1;case uc:return 4;case ns:case Vd:return 16;case dc:return 536870912;default:return 16}default:return 16}}var xt=null,Mi=null,Hr=null;function jc(){if(Hr)return Hr;var e,t=Mi,n=t.length,r,l="value"in xt?xt.value:xt.textContent,i=l.length;for(e=0;e<n&&t[e]===l[e];e++);var o=n-e;for(r=1;r<=o&&t[n-r]===l[i-r];r++);return Hr=l.slice(e,1<r?1-r:void 0)}function Br(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Tr(){return!0}function Eo(){return!1}function De(e){function t(n,r,l,i,o){this._reactName=n,this._targetInst=l,this.type=r,this.nativeEvent=i,this.target=o,this.currentTarget=null;for(var a in e)e.hasOwnProperty(a)&&(n=e[a],this[a]=n?n(i):i[a]);return this.isDefaultPrevented=(i.defaultPrevented!=null?i.defaultPrevented:i.returnValue===!1)?Tr:Eo,this.isPropagationStopped=Eo,this}return Z(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=Tr)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=Tr)},persist:function(){},isPersistent:Tr}),t}var Tn={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Ai=De(Tn),vr=Z({},Tn,{view:0,detail:0}),tm=De(vr),Gs,qs,zn,Ls=Z({},vr,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Ii,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==zn&&(zn&&e.type==="mousemove"?(Gs=e.screenX-zn.screenX,qs=e.screenY-zn.screenY):qs=Gs=0,zn=e),Gs)},movementY:function(e){return"movementY"in e?e.movementY:qs}}),Lo=De(Ls),nm=Z({},Ls,{dataTransfer:0}),rm=De(nm),sm=Z({},vr,{relatedTarget:0}),Ys=De(sm),lm=Z({},Tn,{animationName:0,elapsedTime:0,pseudoElement:0}),im=De(lm),om=Z({},Tn,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),am=De(om),cm=Z({},Tn,{data:0}),To=De(cm),um={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},dm={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},mm={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function fm(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=mm[e])?!!t[e]:!1}function Ii(){return fm}var pm=Z({},vr,{key:function(e){if(e.key){var t=um[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Br(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?dm[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Ii,charCode:function(e){return e.type==="keypress"?Br(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Br(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),hm=De(pm),gm=Z({},Ls,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Do=De(gm),xm=Z({},vr,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Ii}),ym=De(xm),vm=Z({},Tn,{propertyName:0,elapsedTime:0,pseudoElement:0}),jm=De(vm),wm=Z({},Ls,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Nm=De(wm),Sm=[9,13,27,32],_i=it&&"CompositionEvent"in window,Qn=null;it&&"documentMode"in document&&(Qn=document.documentMode);var km=it&&"TextEvent"in window&&!Qn,wc=it&&(!_i||Qn&&8<Qn&&11>=Qn),Mo=String.fromCharCode(32),Ao=!1;function Nc(e,t){switch(e){case"keyup":return Sm.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Sc(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var rn=!1;function bm(e,t){switch(e){case"compositionend":return Sc(t);case"keypress":return t.which!==32?null:(Ao=!0,Mo);case"textInput":return e=t.data,e===Mo&&Ao?null:e;default:return null}}function Cm(e,t){if(rn)return e==="compositionend"||!_i&&Nc(e,t)?(e=jc(),Hr=Mi=xt=null,rn=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return wc&&t.locale!=="ko"?null:t.data;default:return null}}var Em={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Io(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!Em[e.type]:t==="textarea"}function kc(e,t,n,r){tc(r),t=is(t,"onChange"),0<t.length&&(n=new Ai("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var Kn=null,lr=null;function Lm(e){Pc(e,0)}function Ts(e){var t=on(e);if(Ga(t))return e}function Tm(e,t){if(e==="change")return t}var bc=!1;if(it){var Xs;if(it){var Zs="oninput"in document;if(!Zs){var _o=document.createElement("div");_o.setAttribute("oninput","return;"),Zs=typeof _o.oninput=="function"}Xs=Zs}else Xs=!1;bc=Xs&&(!document.documentMode||9<document.documentMode)}function Po(){Kn&&(Kn.detachEvent("onpropertychange",Cc),lr=Kn=null)}function Cc(e){if(e.propertyName==="value"&&Ts(lr)){var t=[];kc(t,lr,e,Ci(e)),lc(Lm,t)}}function Dm(e,t,n){e==="focusin"?(Po(),Kn=t,lr=n,Kn.attachEvent("onpropertychange",Cc)):e==="focusout"&&Po()}function Mm(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Ts(lr)}function Am(e,t){if(e==="click")return Ts(t)}function Im(e,t){if(e==="input"||e==="change")return Ts(t)}function _m(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var We=typeof Object.is=="function"?Object.is:_m;function ir(e,t){if(We(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var l=n[r];if(!xl.call(t,l)||!We(e[l],t[l]))return!1}return!0}function zo(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Ro(e,t){var n=zo(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=zo(n)}}function Ec(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?Ec(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function Lc(){for(var e=window,t=Jr();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=Jr(e.document)}return t}function Pi(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function Pm(e){var t=Lc(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&Ec(n.ownerDocument.documentElement,n)){if(r!==null&&Pi(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var l=n.textContent.length,i=Math.min(r.start,l);r=r.end===void 0?i:Math.min(r.end,l),!e.extend&&i>r&&(l=r,r=i,i=l),l=Ro(n,i);var o=Ro(n,r);l&&o&&(e.rangeCount!==1||e.anchorNode!==l.node||e.anchorOffset!==l.offset||e.focusNode!==o.node||e.focusOffset!==o.offset)&&(t=t.createRange(),t.setStart(l.node,l.offset),e.removeAllRanges(),i>r?(e.addRange(t),e.extend(o.node,o.offset)):(t.setEnd(o.node,o.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var zm=it&&"documentMode"in document&&11>=document.documentMode,sn=null,zl=null,Gn=null,Rl=!1;function $o(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;Rl||sn==null||sn!==Jr(r)||(r=sn,"selectionStart"in r&&Pi(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),Gn&&ir(Gn,r)||(Gn=r,r=is(zl,"onSelect"),0<r.length&&(t=new Ai("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=sn)))}function Dr(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var ln={animationend:Dr("Animation","AnimationEnd"),animationiteration:Dr("Animation","AnimationIteration"),animationstart:Dr("Animation","AnimationStart"),transitionend:Dr("Transition","TransitionEnd")},Js={},Tc={};it&&(Tc=document.createElement("div").style,"AnimationEvent"in window||(delete ln.animationend.animation,delete ln.animationiteration.animation,delete ln.animationstart.animation),"TransitionEvent"in window||delete ln.transitionend.transition);function Ds(e){if(Js[e])return Js[e];if(!ln[e])return e;var t=ln[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in Tc)return Js[e]=t[n];return e}var Dc=Ds("animationend"),Mc=Ds("animationiteration"),Ac=Ds("animationstart"),Ic=Ds("transitionend"),_c=new Map,Oo="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function At(e,t){_c.set(e,t),Xt(t,[e])}for(var el=0;el<Oo.length;el++){var tl=Oo[el],Rm=tl.toLowerCase(),$m=tl[0].toUpperCase()+tl.slice(1);At(Rm,"on"+$m)}At(Dc,"onAnimationEnd");At(Mc,"onAnimationIteration");At(Ac,"onAnimationStart");At("dblclick","onDoubleClick");At("focusin","onFocus");At("focusout","onBlur");At(Ic,"onTransitionEnd");jn("onMouseEnter",["mouseout","mouseover"]);jn("onMouseLeave",["mouseout","mouseover"]);jn("onPointerEnter",["pointerout","pointerover"]);jn("onPointerLeave",["pointerout","pointerover"]);Xt("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));Xt("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));Xt("onBeforeInput",["compositionend","keypress","textInput","paste"]);Xt("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));Xt("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));Xt("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Hn="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Om=new Set("cancel close invalid load scroll toggle".split(" ").concat(Hn));function Uo(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,Rd(r,t,void 0,e),e.currentTarget=null}function Pc(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],l=r.event;r=r.listeners;e:{var i=void 0;if(t)for(var o=r.length-1;0<=o;o--){var a=r[o],c=a.instance,f=a.currentTarget;if(a=a.listener,c!==i&&l.isPropagationStopped())break e;Uo(l,a,f),i=c}else for(o=0;o<r.length;o++){if(a=r[o],c=a.instance,f=a.currentTarget,a=a.listener,c!==i&&l.isPropagationStopped())break e;Uo(l,a,f),i=c}}}if(ts)throw e=Al,ts=!1,Al=null,e}function K(e,t){var n=t[Vl];n===void 0&&(n=t[Vl]=new Set);var r=e+"__bubble";n.has(r)||(zc(t,e,2,!1),n.add(r))}function nl(e,t,n){var r=0;t&&(r|=4),zc(n,e,r,t)}var Mr="_reactListening"+Math.random().toString(36).slice(2);function or(e){if(!e[Mr]){e[Mr]=!0,Ha.forEach(function(n){n!=="selectionchange"&&(Om.has(n)||nl(n,!1,e),nl(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Mr]||(t[Mr]=!0,nl("selectionchange",!1,t))}}function zc(e,t,n,r){switch(vc(t)){case 1:var l=Jd;break;case 4:l=em;break;default:l=Di}n=l.bind(null,t,n,e),l=void 0,!Ml||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(l=!0),r?l!==void 0?e.addEventListener(t,n,{capture:!0,passive:l}):e.addEventListener(t,n,!0):l!==void 0?e.addEventListener(t,n,{passive:l}):e.addEventListener(t,n,!1)}function rl(e,t,n,r,l){var i=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var o=r.tag;if(o===3||o===4){var a=r.stateNode.containerInfo;if(a===l||a.nodeType===8&&a.parentNode===l)break;if(o===4)for(o=r.return;o!==null;){var c=o.tag;if((c===3||c===4)&&(c=o.stateNode.containerInfo,c===l||c.nodeType===8&&c.parentNode===l))return;o=o.return}for(;a!==null;){if(o=Ft(a),o===null)return;if(c=o.tag,c===5||c===6){r=i=o;continue e}a=a.parentNode}}r=r.return}lc(function(){var f=i,x=Ci(n),g=[];e:{var y=_c.get(e);if(y!==void 0){var v=Ai,w=e;switch(e){case"keypress":if(Br(n)===0)break e;case"keydown":case"keyup":v=hm;break;case"focusin":w="focus",v=Ys;break;case"focusout":w="blur",v=Ys;break;case"beforeblur":case"afterblur":v=Ys;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":v=Lo;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":v=rm;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":v=ym;break;case Dc:case Mc:case Ac:v=im;break;case Ic:v=jm;break;case"scroll":v=tm;break;case"wheel":v=Nm;break;case"copy":case"cut":case"paste":v=am;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":v=Do}var h=(t&4)!==0,N=!h&&e==="scroll",m=h?y!==null?y+"Capture":null:y;h=[];for(var d=f,u;d!==null;){u=d;var p=u.stateNode;if(u.tag===5&&p!==null&&(u=p,m!==null&&(p=tr(d,m),p!=null&&h.push(ar(d,p,u)))),N)break;d=d.return}0<h.length&&(y=new v(y,w,null,n,x),g.push({event:y,listeners:h}))}}if(!(t&7)){e:{if(y=e==="mouseover"||e==="pointerover",v=e==="mouseout"||e==="pointerout",y&&n!==Tl&&(w=n.relatedTarget||n.fromElement)&&(Ft(w)||w[ot]))break e;if((v||y)&&(y=x.window===x?x:(y=x.ownerDocument)?y.defaultView||y.parentWindow:window,v?(w=n.relatedTarget||n.toElement,v=f,w=w?Ft(w):null,w!==null&&(N=Zt(w),w!==N||w.tag!==5&&w.tag!==6)&&(w=null)):(v=null,w=f),v!==w)){if(h=Lo,p="onMouseLeave",m="onMouseEnter",d="mouse",(e==="pointerout"||e==="pointerover")&&(h=Do,p="onPointerLeave",m="onPointerEnter",d="pointer"),N=v==null?y:on(v),u=w==null?y:on(w),y=new h(p,d+"leave",v,n,x),y.target=N,y.relatedTarget=u,p=null,Ft(x)===f&&(h=new h(m,d+"enter",w,n,x),h.target=u,h.relatedTarget=N,p=h),N=p,v&&w)t:{for(h=v,m=w,d=0,u=h;u;u=en(u))d++;for(u=0,p=m;p;p=en(p))u++;for(;0<d-u;)h=en(h),d--;for(;0<u-d;)m=en(m),u--;for(;d--;){if(h===m||m!==null&&h===m.alternate)break t;h=en(h),m=en(m)}h=null}else h=null;v!==null&&Fo(g,y,v,h,!1),w!==null&&N!==null&&Fo(g,N,w,h,!0)}}e:{if(y=f?on(f):window,v=y.nodeName&&y.nodeName.toLowerCase(),v==="select"||v==="input"&&y.type==="file")var j=Tm;else if(Io(y))if(bc)j=Im;else{j=Mm;var S=Dm}else(v=y.nodeName)&&v.toLowerCase()==="input"&&(y.type==="checkbox"||y.type==="radio")&&(j=Am);if(j&&(j=j(e,f))){kc(g,j,n,x);break e}S&&S(e,y,f),e==="focusout"&&(S=y._wrapperState)&&S.controlled&&y.type==="number"&&kl(y,"number",y.value)}switch(S=f?on(f):window,e){case"focusin":(Io(S)||S.contentEditable==="true")&&(sn=S,zl=f,Gn=null);break;case"focusout":Gn=zl=sn=null;break;case"mousedown":Rl=!0;break;case"contextmenu":case"mouseup":case"dragend":Rl=!1,$o(g,n,x);break;case"selectionchange":if(zm)break;case"keydown":case"keyup":$o(g,n,x)}var T;if(_i)e:{switch(e){case"compositionstart":var A="onCompositionStart";break e;case"compositionend":A="onCompositionEnd";break e;case"compositionupdate":A="onCompositionUpdate";break e}A=void 0}else rn?Nc(e,n)&&(A="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(A="onCompositionStart");A&&(wc&&n.locale!=="ko"&&(rn||A!=="onCompositionStart"?A==="onCompositionEnd"&&rn&&(T=jc()):(xt=x,Mi="value"in xt?xt.value:xt.textContent,rn=!0)),S=is(f,A),0<S.length&&(A=new To(A,e,null,n,x),g.push({event:A,listeners:S}),T?A.data=T:(T=Sc(n),T!==null&&(A.data=T)))),(T=km?bm(e,n):Cm(e,n))&&(f=is(f,"onBeforeInput"),0<f.length&&(x=new To("onBeforeInput","beforeinput",null,n,x),g.push({event:x,listeners:f}),x.data=T))}Pc(g,t)})}function ar(e,t,n){return{instance:e,listener:t,currentTarget:n}}function is(e,t){for(var n=t+"Capture",r=[];e!==null;){var l=e,i=l.stateNode;l.tag===5&&i!==null&&(l=i,i=tr(e,n),i!=null&&r.unshift(ar(e,i,l)),i=tr(e,t),i!=null&&r.push(ar(e,i,l))),e=e.return}return r}function en(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function Fo(e,t,n,r,l){for(var i=t._reactName,o=[];n!==null&&n!==r;){var a=n,c=a.alternate,f=a.stateNode;if(c!==null&&c===r)break;a.tag===5&&f!==null&&(a=f,l?(c=tr(n,i),c!=null&&o.unshift(ar(n,c,a))):l||(c=tr(n,i),c!=null&&o.push(ar(n,c,a)))),n=n.return}o.length!==0&&e.push({event:t,listeners:o})}var Um=/\r\n?/g,Fm=/\u0000|\uFFFD/g;function Vo(e){return(typeof e=="string"?e:""+e).replace(Um,`
`).replace(Fm,"")}function Ar(e,t,n){if(t=Vo(t),Vo(e)!==t&&n)throw Error(k(425))}function os(){}var $l=null,Ol=null;function Ul(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Fl=typeof setTimeout=="function"?setTimeout:void 0,Vm=typeof clearTimeout=="function"?clearTimeout:void 0,Ho=typeof Promise=="function"?Promise:void 0,Hm=typeof queueMicrotask=="function"?queueMicrotask:typeof Ho<"u"?function(e){return Ho.resolve(null).then(e).catch(Bm)}:Fl;function Bm(e){setTimeout(function(){throw e})}function sl(e,t){var n=t,r=0;do{var l=n.nextSibling;if(e.removeChild(n),l&&l.nodeType===8)if(n=l.data,n==="/$"){if(r===0){e.removeChild(l),sr(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=l}while(n);sr(t)}function St(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function Bo(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var Dn=Math.random().toString(36).slice(2),qe="__reactFiber$"+Dn,cr="__reactProps$"+Dn,ot="__reactContainer$"+Dn,Vl="__reactEvents$"+Dn,Wm="__reactListeners$"+Dn,Qm="__reactHandles$"+Dn;function Ft(e){var t=e[qe];if(t)return t;for(var n=e.parentNode;n;){if(t=n[ot]||n[qe]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=Bo(e);e!==null;){if(n=e[qe])return n;e=Bo(e)}return t}e=n,n=e.parentNode}return null}function jr(e){return e=e[qe]||e[ot],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function on(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(k(33))}function Ms(e){return e[cr]||null}var Hl=[],an=-1;function It(e){return{current:e}}function G(e){0>an||(e.current=Hl[an],Hl[an]=null,an--)}function W(e,t){an++,Hl[an]=e.current,e.current=t}var Mt={},pe=It(Mt),Ne=It(!1),Qt=Mt;function wn(e,t){var n=e.type.contextTypes;if(!n)return Mt;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var l={},i;for(i in n)l[i]=t[i];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=l),l}function Se(e){return e=e.childContextTypes,e!=null}function as(){G(Ne),G(pe)}function Wo(e,t,n){if(pe.current!==Mt)throw Error(k(168));W(pe,t),W(Ne,n)}function Rc(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var l in r)if(!(l in t))throw Error(k(108,Dd(e)||"Unknown",l));return Z({},n,r)}function cs(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||Mt,Qt=pe.current,W(pe,e),W(Ne,Ne.current),!0}function Qo(e,t,n){var r=e.stateNode;if(!r)throw Error(k(169));n?(e=Rc(e,t,Qt),r.__reactInternalMemoizedMergedChildContext=e,G(Ne),G(pe),W(pe,e)):G(Ne),W(Ne,n)}var tt=null,As=!1,ll=!1;function $c(e){tt===null?tt=[e]:tt.push(e)}function Km(e){As=!0,$c(e)}function _t(){if(!ll&&tt!==null){ll=!0;var e=0,t=H;try{var n=tt;for(H=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}tt=null,As=!1}catch(l){throw tt!==null&&(tt=tt.slice(e+1)),cc(Ei,_t),l}finally{H=t,ll=!1}}return null}var cn=[],un=0,us=null,ds=0,Ae=[],Ie=0,Kt=null,nt=1,rt="";function Rt(e,t){cn[un++]=ds,cn[un++]=us,us=e,ds=t}function Oc(e,t,n){Ae[Ie++]=nt,Ae[Ie++]=rt,Ae[Ie++]=Kt,Kt=e;var r=nt;e=rt;var l=32-He(r)-1;r&=~(1<<l),n+=1;var i=32-He(t)+l;if(30<i){var o=l-l%5;i=(r&(1<<o)-1).toString(32),r>>=o,l-=o,nt=1<<32-He(t)+l|n<<l|r,rt=i+e}else nt=1<<i|n<<l|r,rt=e}function zi(e){e.return!==null&&(Rt(e,1),Oc(e,1,0))}function Ri(e){for(;e===us;)us=cn[--un],cn[un]=null,ds=cn[--un],cn[un]=null;for(;e===Kt;)Kt=Ae[--Ie],Ae[Ie]=null,rt=Ae[--Ie],Ae[Ie]=null,nt=Ae[--Ie],Ae[Ie]=null}var Ee=null,Ce=null,q=!1,Fe=null;function Uc(e,t){var n=_e(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function Ko(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,Ee=e,Ce=St(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,Ee=e,Ce=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=Kt!==null?{id:nt,overflow:rt}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=_e(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,Ee=e,Ce=null,!0):!1;default:return!1}}function Bl(e){return(e.mode&1)!==0&&(e.flags&128)===0}function Wl(e){if(q){var t=Ce;if(t){var n=t;if(!Ko(e,t)){if(Bl(e))throw Error(k(418));t=St(n.nextSibling);var r=Ee;t&&Ko(e,t)?Uc(r,n):(e.flags=e.flags&-4097|2,q=!1,Ee=e)}}else{if(Bl(e))throw Error(k(418));e.flags=e.flags&-4097|2,q=!1,Ee=e}}}function Go(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;Ee=e}function Ir(e){if(e!==Ee)return!1;if(!q)return Go(e),q=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!Ul(e.type,e.memoizedProps)),t&&(t=Ce)){if(Bl(e))throw Fc(),Error(k(418));for(;t;)Uc(e,t),t=St(t.nextSibling)}if(Go(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(k(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){Ce=St(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}Ce=null}}else Ce=Ee?St(e.stateNode.nextSibling):null;return!0}function Fc(){for(var e=Ce;e;)e=St(e.nextSibling)}function Nn(){Ce=Ee=null,q=!1}function $i(e){Fe===null?Fe=[e]:Fe.push(e)}var Gm=ut.ReactCurrentBatchConfig;function Rn(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(k(309));var r=n.stateNode}if(!r)throw Error(k(147,e));var l=r,i=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===i?t.ref:(t=function(o){var a=l.refs;o===null?delete a[i]:a[i]=o},t._stringRef=i,t)}if(typeof e!="string")throw Error(k(284));if(!n._owner)throw Error(k(290,e))}return e}function _r(e,t){throw e=Object.prototype.toString.call(t),Error(k(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function qo(e){var t=e._init;return t(e._payload)}function Vc(e){function t(m,d){if(e){var u=m.deletions;u===null?(m.deletions=[d],m.flags|=16):u.push(d)}}function n(m,d){if(!e)return null;for(;d!==null;)t(m,d),d=d.sibling;return null}function r(m,d){for(m=new Map;d!==null;)d.key!==null?m.set(d.key,d):m.set(d.index,d),d=d.sibling;return m}function l(m,d){return m=Et(m,d),m.index=0,m.sibling=null,m}function i(m,d,u){return m.index=u,e?(u=m.alternate,u!==null?(u=u.index,u<d?(m.flags|=2,d):u):(m.flags|=2,d)):(m.flags|=1048576,d)}function o(m){return e&&m.alternate===null&&(m.flags|=2),m}function a(m,d,u,p){return d===null||d.tag!==6?(d=ml(u,m.mode,p),d.return=m,d):(d=l(d,u),d.return=m,d)}function c(m,d,u,p){var j=u.type;return j===nn?x(m,d,u.props.children,p,u.key):d!==null&&(d.elementType===j||typeof j=="object"&&j!==null&&j.$$typeof===ft&&qo(j)===d.type)?(p=l(d,u.props),p.ref=Rn(m,d,u),p.return=m,p):(p=Xr(u.type,u.key,u.props,null,m.mode,p),p.ref=Rn(m,d,u),p.return=m,p)}function f(m,d,u,p){return d===null||d.tag!==4||d.stateNode.containerInfo!==u.containerInfo||d.stateNode.implementation!==u.implementation?(d=fl(u,m.mode,p),d.return=m,d):(d=l(d,u.children||[]),d.return=m,d)}function x(m,d,u,p,j){return d===null||d.tag!==7?(d=Wt(u,m.mode,p,j),d.return=m,d):(d=l(d,u),d.return=m,d)}function g(m,d,u){if(typeof d=="string"&&d!==""||typeof d=="number")return d=ml(""+d,m.mode,u),d.return=m,d;if(typeof d=="object"&&d!==null){switch(d.$$typeof){case Sr:return u=Xr(d.type,d.key,d.props,null,m.mode,u),u.ref=Rn(m,null,d),u.return=m,u;case tn:return d=fl(d,m.mode,u),d.return=m,d;case ft:var p=d._init;return g(m,p(d._payload),u)}if(Fn(d)||An(d))return d=Wt(d,m.mode,u,null),d.return=m,d;_r(m,d)}return null}function y(m,d,u,p){var j=d!==null?d.key:null;if(typeof u=="string"&&u!==""||typeof u=="number")return j!==null?null:a(m,d,""+u,p);if(typeof u=="object"&&u!==null){switch(u.$$typeof){case Sr:return u.key===j?c(m,d,u,p):null;case tn:return u.key===j?f(m,d,u,p):null;case ft:return j=u._init,y(m,d,j(u._payload),p)}if(Fn(u)||An(u))return j!==null?null:x(m,d,u,p,null);_r(m,u)}return null}function v(m,d,u,p,j){if(typeof p=="string"&&p!==""||typeof p=="number")return m=m.get(u)||null,a(d,m,""+p,j);if(typeof p=="object"&&p!==null){switch(p.$$typeof){case Sr:return m=m.get(p.key===null?u:p.key)||null,c(d,m,p,j);case tn:return m=m.get(p.key===null?u:p.key)||null,f(d,m,p,j);case ft:var S=p._init;return v(m,d,u,S(p._payload),j)}if(Fn(p)||An(p))return m=m.get(u)||null,x(d,m,p,j,null);_r(d,p)}return null}function w(m,d,u,p){for(var j=null,S=null,T=d,A=d=0,V=null;T!==null&&A<u.length;A++){T.index>A?(V=T,T=null):V=T.sibling;var _=y(m,T,u[A],p);if(_===null){T===null&&(T=V);break}e&&T&&_.alternate===null&&t(m,T),d=i(_,d,A),S===null?j=_:S.sibling=_,S=_,T=V}if(A===u.length)return n(m,T),q&&Rt(m,A),j;if(T===null){for(;A<u.length;A++)T=g(m,u[A],p),T!==null&&(d=i(T,d,A),S===null?j=T:S.sibling=T,S=T);return q&&Rt(m,A),j}for(T=r(m,T);A<u.length;A++)V=v(T,m,A,u[A],p),V!==null&&(e&&V.alternate!==null&&T.delete(V.key===null?A:V.key),d=i(V,d,A),S===null?j=V:S.sibling=V,S=V);return e&&T.forEach(function(ve){return t(m,ve)}),q&&Rt(m,A),j}function h(m,d,u,p){var j=An(u);if(typeof j!="function")throw Error(k(150));if(u=j.call(u),u==null)throw Error(k(151));for(var S=j=null,T=d,A=d=0,V=null,_=u.next();T!==null&&!_.done;A++,_=u.next()){T.index>A?(V=T,T=null):V=T.sibling;var ve=y(m,T,_.value,p);if(ve===null){T===null&&(T=V);break}e&&T&&ve.alternate===null&&t(m,T),d=i(ve,d,A),S===null?j=ve:S.sibling=ve,S=ve,T=V}if(_.done)return n(m,T),q&&Rt(m,A),j;if(T===null){for(;!_.done;A++,_=u.next())_=g(m,_.value,p),_!==null&&(d=i(_,d,A),S===null?j=_:S.sibling=_,S=_);return q&&Rt(m,A),j}for(T=r(m,T);!_.done;A++,_=u.next())_=v(T,m,A,_.value,p),_!==null&&(e&&_.alternate!==null&&T.delete(_.key===null?A:_.key),d=i(_,d,A),S===null?j=_:S.sibling=_,S=_);return e&&T.forEach(function(Ze){return t(m,Ze)}),q&&Rt(m,A),j}function N(m,d,u,p){if(typeof u=="object"&&u!==null&&u.type===nn&&u.key===null&&(u=u.props.children),typeof u=="object"&&u!==null){switch(u.$$typeof){case Sr:e:{for(var j=u.key,S=d;S!==null;){if(S.key===j){if(j=u.type,j===nn){if(S.tag===7){n(m,S.sibling),d=l(S,u.props.children),d.return=m,m=d;break e}}else if(S.elementType===j||typeof j=="object"&&j!==null&&j.$$typeof===ft&&qo(j)===S.type){n(m,S.sibling),d=l(S,u.props),d.ref=Rn(m,S,u),d.return=m,m=d;break e}n(m,S);break}else t(m,S);S=S.sibling}u.type===nn?(d=Wt(u.props.children,m.mode,p,u.key),d.return=m,m=d):(p=Xr(u.type,u.key,u.props,null,m.mode,p),p.ref=Rn(m,d,u),p.return=m,m=p)}return o(m);case tn:e:{for(S=u.key;d!==null;){if(d.key===S)if(d.tag===4&&d.stateNode.containerInfo===u.containerInfo&&d.stateNode.implementation===u.implementation){n(m,d.sibling),d=l(d,u.children||[]),d.return=m,m=d;break e}else{n(m,d);break}else t(m,d);d=d.sibling}d=fl(u,m.mode,p),d.return=m,m=d}return o(m);case ft:return S=u._init,N(m,d,S(u._payload),p)}if(Fn(u))return w(m,d,u,p);if(An(u))return h(m,d,u,p);_r(m,u)}return typeof u=="string"&&u!==""||typeof u=="number"?(u=""+u,d!==null&&d.tag===6?(n(m,d.sibling),d=l(d,u),d.return=m,m=d):(n(m,d),d=ml(u,m.mode,p),d.return=m,m=d),o(m)):n(m,d)}return N}var Sn=Vc(!0),Hc=Vc(!1),ms=It(null),fs=null,dn=null,Oi=null;function Ui(){Oi=dn=fs=null}function Fi(e){var t=ms.current;G(ms),e._currentValue=t}function Ql(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function yn(e,t){fs=e,Oi=dn=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(we=!0),e.firstContext=null)}function ze(e){var t=e._currentValue;if(Oi!==e)if(e={context:e,memoizedValue:t,next:null},dn===null){if(fs===null)throw Error(k(308));dn=e,fs.dependencies={lanes:0,firstContext:e}}else dn=dn.next=e;return t}var Vt=null;function Vi(e){Vt===null?Vt=[e]:Vt.push(e)}function Bc(e,t,n,r){var l=t.interleaved;return l===null?(n.next=n,Vi(t)):(n.next=l.next,l.next=n),t.interleaved=n,at(e,r)}function at(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var pt=!1;function Hi(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Wc(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function lt(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function kt(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,U&2){var l=r.pending;return l===null?t.next=t:(t.next=l.next,l.next=t),r.pending=t,at(e,n)}return l=r.interleaved,l===null?(t.next=t,Vi(r)):(t.next=l.next,l.next=t),r.interleaved=t,at(e,n)}function Wr(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Li(e,n)}}function Yo(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var l=null,i=null;if(n=n.firstBaseUpdate,n!==null){do{var o={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};i===null?l=i=o:i=i.next=o,n=n.next}while(n!==null);i===null?l=i=t:i=i.next=t}else l=i=t;n={baseState:r.baseState,firstBaseUpdate:l,lastBaseUpdate:i,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function ps(e,t,n,r){var l=e.updateQueue;pt=!1;var i=l.firstBaseUpdate,o=l.lastBaseUpdate,a=l.shared.pending;if(a!==null){l.shared.pending=null;var c=a,f=c.next;c.next=null,o===null?i=f:o.next=f,o=c;var x=e.alternate;x!==null&&(x=x.updateQueue,a=x.lastBaseUpdate,a!==o&&(a===null?x.firstBaseUpdate=f:a.next=f,x.lastBaseUpdate=c))}if(i!==null){var g=l.baseState;o=0,x=f=c=null,a=i;do{var y=a.lane,v=a.eventTime;if((r&y)===y){x!==null&&(x=x.next={eventTime:v,lane:0,tag:a.tag,payload:a.payload,callback:a.callback,next:null});e:{var w=e,h=a;switch(y=t,v=n,h.tag){case 1:if(w=h.payload,typeof w=="function"){g=w.call(v,g,y);break e}g=w;break e;case 3:w.flags=w.flags&-65537|128;case 0:if(w=h.payload,y=typeof w=="function"?w.call(v,g,y):w,y==null)break e;g=Z({},g,y);break e;case 2:pt=!0}}a.callback!==null&&a.lane!==0&&(e.flags|=64,y=l.effects,y===null?l.effects=[a]:y.push(a))}else v={eventTime:v,lane:y,tag:a.tag,payload:a.payload,callback:a.callback,next:null},x===null?(f=x=v,c=g):x=x.next=v,o|=y;if(a=a.next,a===null){if(a=l.shared.pending,a===null)break;y=a,a=y.next,y.next=null,l.lastBaseUpdate=y,l.shared.pending=null}}while(1);if(x===null&&(c=g),l.baseState=c,l.firstBaseUpdate=f,l.lastBaseUpdate=x,t=l.shared.interleaved,t!==null){l=t;do o|=l.lane,l=l.next;while(l!==t)}else i===null&&(l.shared.lanes=0);qt|=o,e.lanes=o,e.memoizedState=g}}function Xo(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],l=r.callback;if(l!==null){if(r.callback=null,r=n,typeof l!="function")throw Error(k(191,l));l.call(r)}}}var wr={},Xe=It(wr),ur=It(wr),dr=It(wr);function Ht(e){if(e===wr)throw Error(k(174));return e}function Bi(e,t){switch(W(dr,t),W(ur,e),W(Xe,wr),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:Cl(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=Cl(t,e)}G(Xe),W(Xe,t)}function kn(){G(Xe),G(ur),G(dr)}function Qc(e){Ht(dr.current);var t=Ht(Xe.current),n=Cl(t,e.type);t!==n&&(W(ur,e),W(Xe,n))}function Wi(e){ur.current===e&&(G(Xe),G(ur))}var Y=It(0);function hs(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var il=[];function Qi(){for(var e=0;e<il.length;e++)il[e]._workInProgressVersionPrimary=null;il.length=0}var Qr=ut.ReactCurrentDispatcher,ol=ut.ReactCurrentBatchConfig,Gt=0,X=null,re=null,le=null,gs=!1,qn=!1,mr=0,qm=0;function de(){throw Error(k(321))}function Ki(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!We(e[n],t[n]))return!1;return!0}function Gi(e,t,n,r,l,i){if(Gt=i,X=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,Qr.current=e===null||e.memoizedState===null?Jm:ef,e=n(r,l),qn){i=0;do{if(qn=!1,mr=0,25<=i)throw Error(k(301));i+=1,le=re=null,t.updateQueue=null,Qr.current=tf,e=n(r,l)}while(qn)}if(Qr.current=xs,t=re!==null&&re.next!==null,Gt=0,le=re=X=null,gs=!1,t)throw Error(k(300));return e}function qi(){var e=mr!==0;return mr=0,e}function Ge(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return le===null?X.memoizedState=le=e:le=le.next=e,le}function Re(){if(re===null){var e=X.alternate;e=e!==null?e.memoizedState:null}else e=re.next;var t=le===null?X.memoizedState:le.next;if(t!==null)le=t,re=e;else{if(e===null)throw Error(k(310));re=e,e={memoizedState:re.memoizedState,baseState:re.baseState,baseQueue:re.baseQueue,queue:re.queue,next:null},le===null?X.memoizedState=le=e:le=le.next=e}return le}function fr(e,t){return typeof t=="function"?t(e):t}function al(e){var t=Re(),n=t.queue;if(n===null)throw Error(k(311));n.lastRenderedReducer=e;var r=re,l=r.baseQueue,i=n.pending;if(i!==null){if(l!==null){var o=l.next;l.next=i.next,i.next=o}r.baseQueue=l=i,n.pending=null}if(l!==null){i=l.next,r=r.baseState;var a=o=null,c=null,f=i;do{var x=f.lane;if((Gt&x)===x)c!==null&&(c=c.next={lane:0,action:f.action,hasEagerState:f.hasEagerState,eagerState:f.eagerState,next:null}),r=f.hasEagerState?f.eagerState:e(r,f.action);else{var g={lane:x,action:f.action,hasEagerState:f.hasEagerState,eagerState:f.eagerState,next:null};c===null?(a=c=g,o=r):c=c.next=g,X.lanes|=x,qt|=x}f=f.next}while(f!==null&&f!==i);c===null?o=r:c.next=a,We(r,t.memoizedState)||(we=!0),t.memoizedState=r,t.baseState=o,t.baseQueue=c,n.lastRenderedState=r}if(e=n.interleaved,e!==null){l=e;do i=l.lane,X.lanes|=i,qt|=i,l=l.next;while(l!==e)}else l===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function cl(e){var t=Re(),n=t.queue;if(n===null)throw Error(k(311));n.lastRenderedReducer=e;var r=n.dispatch,l=n.pending,i=t.memoizedState;if(l!==null){n.pending=null;var o=l=l.next;do i=e(i,o.action),o=o.next;while(o!==l);We(i,t.memoizedState)||(we=!0),t.memoizedState=i,t.baseQueue===null&&(t.baseState=i),n.lastRenderedState=i}return[i,r]}function Kc(){}function Gc(e,t){var n=X,r=Re(),l=t(),i=!We(r.memoizedState,l);if(i&&(r.memoizedState=l,we=!0),r=r.queue,Yi(Xc.bind(null,n,r,e),[e]),r.getSnapshot!==t||i||le!==null&&le.memoizedState.tag&1){if(n.flags|=2048,pr(9,Yc.bind(null,n,r,l,t),void 0,null),ie===null)throw Error(k(349));Gt&30||qc(n,t,l)}return l}function qc(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=X.updateQueue,t===null?(t={lastEffect:null,stores:null},X.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function Yc(e,t,n,r){t.value=n,t.getSnapshot=r,Zc(t)&&Jc(e)}function Xc(e,t,n){return n(function(){Zc(t)&&Jc(e)})}function Zc(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!We(e,n)}catch{return!0}}function Jc(e){var t=at(e,1);t!==null&&Be(t,e,1,-1)}function Zo(e){var t=Ge();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:fr,lastRenderedState:e},t.queue=e,e=e.dispatch=Zm.bind(null,X,e),[t.memoizedState,e]}function pr(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=X.updateQueue,t===null?(t={lastEffect:null,stores:null},X.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function eu(){return Re().memoizedState}function Kr(e,t,n,r){var l=Ge();X.flags|=e,l.memoizedState=pr(1|t,n,void 0,r===void 0?null:r)}function Is(e,t,n,r){var l=Re();r=r===void 0?null:r;var i=void 0;if(re!==null){var o=re.memoizedState;if(i=o.destroy,r!==null&&Ki(r,o.deps)){l.memoizedState=pr(t,n,i,r);return}}X.flags|=e,l.memoizedState=pr(1|t,n,i,r)}function Jo(e,t){return Kr(8390656,8,e,t)}function Yi(e,t){return Is(2048,8,e,t)}function tu(e,t){return Is(4,2,e,t)}function nu(e,t){return Is(4,4,e,t)}function ru(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function su(e,t,n){return n=n!=null?n.concat([e]):null,Is(4,4,ru.bind(null,t,e),n)}function Xi(){}function lu(e,t){var n=Re();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Ki(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function iu(e,t){var n=Re();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Ki(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function ou(e,t,n){return Gt&21?(We(n,t)||(n=mc(),X.lanes|=n,qt|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,we=!0),e.memoizedState=n)}function Ym(e,t){var n=H;H=n!==0&&4>n?n:4,e(!0);var r=ol.transition;ol.transition={};try{e(!1),t()}finally{H=n,ol.transition=r}}function au(){return Re().memoizedState}function Xm(e,t,n){var r=Ct(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},cu(e))uu(t,n);else if(n=Bc(e,t,n,r),n!==null){var l=ge();Be(n,e,r,l),du(n,t,r)}}function Zm(e,t,n){var r=Ct(e),l={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(cu(e))uu(t,l);else{var i=e.alternate;if(e.lanes===0&&(i===null||i.lanes===0)&&(i=t.lastRenderedReducer,i!==null))try{var o=t.lastRenderedState,a=i(o,n);if(l.hasEagerState=!0,l.eagerState=a,We(a,o)){var c=t.interleaved;c===null?(l.next=l,Vi(t)):(l.next=c.next,c.next=l),t.interleaved=l;return}}catch{}finally{}n=Bc(e,t,l,r),n!==null&&(l=ge(),Be(n,e,r,l),du(n,t,r))}}function cu(e){var t=e.alternate;return e===X||t!==null&&t===X}function uu(e,t){qn=gs=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function du(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Li(e,n)}}var xs={readContext:ze,useCallback:de,useContext:de,useEffect:de,useImperativeHandle:de,useInsertionEffect:de,useLayoutEffect:de,useMemo:de,useReducer:de,useRef:de,useState:de,useDebugValue:de,useDeferredValue:de,useTransition:de,useMutableSource:de,useSyncExternalStore:de,useId:de,unstable_isNewReconciler:!1},Jm={readContext:ze,useCallback:function(e,t){return Ge().memoizedState=[e,t===void 0?null:t],e},useContext:ze,useEffect:Jo,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,Kr(4194308,4,ru.bind(null,t,e),n)},useLayoutEffect:function(e,t){return Kr(4194308,4,e,t)},useInsertionEffect:function(e,t){return Kr(4,2,e,t)},useMemo:function(e,t){var n=Ge();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=Ge();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=Xm.bind(null,X,e),[r.memoizedState,e]},useRef:function(e){var t=Ge();return e={current:e},t.memoizedState=e},useState:Zo,useDebugValue:Xi,useDeferredValue:function(e){return Ge().memoizedState=e},useTransition:function(){var e=Zo(!1),t=e[0];return e=Ym.bind(null,e[1]),Ge().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=X,l=Ge();if(q){if(n===void 0)throw Error(k(407));n=n()}else{if(n=t(),ie===null)throw Error(k(349));Gt&30||qc(r,t,n)}l.memoizedState=n;var i={value:n,getSnapshot:t};return l.queue=i,Jo(Xc.bind(null,r,i,e),[e]),r.flags|=2048,pr(9,Yc.bind(null,r,i,n,t),void 0,null),n},useId:function(){var e=Ge(),t=ie.identifierPrefix;if(q){var n=rt,r=nt;n=(r&~(1<<32-He(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=mr++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=qm++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},ef={readContext:ze,useCallback:lu,useContext:ze,useEffect:Yi,useImperativeHandle:su,useInsertionEffect:tu,useLayoutEffect:nu,useMemo:iu,useReducer:al,useRef:eu,useState:function(){return al(fr)},useDebugValue:Xi,useDeferredValue:function(e){var t=Re();return ou(t,re.memoizedState,e)},useTransition:function(){var e=al(fr)[0],t=Re().memoizedState;return[e,t]},useMutableSource:Kc,useSyncExternalStore:Gc,useId:au,unstable_isNewReconciler:!1},tf={readContext:ze,useCallback:lu,useContext:ze,useEffect:Yi,useImperativeHandle:su,useInsertionEffect:tu,useLayoutEffect:nu,useMemo:iu,useReducer:cl,useRef:eu,useState:function(){return cl(fr)},useDebugValue:Xi,useDeferredValue:function(e){var t=Re();return re===null?t.memoizedState=e:ou(t,re.memoizedState,e)},useTransition:function(){var e=cl(fr)[0],t=Re().memoizedState;return[e,t]},useMutableSource:Kc,useSyncExternalStore:Gc,useId:au,unstable_isNewReconciler:!1};function Oe(e,t){if(e&&e.defaultProps){t=Z({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function Kl(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:Z({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var _s={isMounted:function(e){return(e=e._reactInternals)?Zt(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=ge(),l=Ct(e),i=lt(r,l);i.payload=t,n!=null&&(i.callback=n),t=kt(e,i,l),t!==null&&(Be(t,e,l,r),Wr(t,e,l))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=ge(),l=Ct(e),i=lt(r,l);i.tag=1,i.payload=t,n!=null&&(i.callback=n),t=kt(e,i,l),t!==null&&(Be(t,e,l,r),Wr(t,e,l))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=ge(),r=Ct(e),l=lt(n,r);l.tag=2,t!=null&&(l.callback=t),t=kt(e,l,r),t!==null&&(Be(t,e,r,n),Wr(t,e,r))}};function ea(e,t,n,r,l,i,o){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,i,o):t.prototype&&t.prototype.isPureReactComponent?!ir(n,r)||!ir(l,i):!0}function mu(e,t,n){var r=!1,l=Mt,i=t.contextType;return typeof i=="object"&&i!==null?i=ze(i):(l=Se(t)?Qt:pe.current,r=t.contextTypes,i=(r=r!=null)?wn(e,l):Mt),t=new t(n,i),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=_s,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=l,e.__reactInternalMemoizedMaskedChildContext=i),t}function ta(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&_s.enqueueReplaceState(t,t.state,null)}function Gl(e,t,n,r){var l=e.stateNode;l.props=n,l.state=e.memoizedState,l.refs={},Hi(e);var i=t.contextType;typeof i=="object"&&i!==null?l.context=ze(i):(i=Se(t)?Qt:pe.current,l.context=wn(e,i)),l.state=e.memoizedState,i=t.getDerivedStateFromProps,typeof i=="function"&&(Kl(e,t,i,n),l.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof l.getSnapshotBeforeUpdate=="function"||typeof l.UNSAFE_componentWillMount!="function"&&typeof l.componentWillMount!="function"||(t=l.state,typeof l.componentWillMount=="function"&&l.componentWillMount(),typeof l.UNSAFE_componentWillMount=="function"&&l.UNSAFE_componentWillMount(),t!==l.state&&_s.enqueueReplaceState(l,l.state,null),ps(e,n,l,r),l.state=e.memoizedState),typeof l.componentDidMount=="function"&&(e.flags|=4194308)}function bn(e,t){try{var n="",r=t;do n+=Td(r),r=r.return;while(r);var l=n}catch(i){l=`
Error generating stack: `+i.message+`
`+i.stack}return{value:e,source:t,stack:l,digest:null}}function ul(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function ql(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var nf=typeof WeakMap=="function"?WeakMap:Map;function fu(e,t,n){n=lt(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){vs||(vs=!0,li=r),ql(e,t)},n}function pu(e,t,n){n=lt(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var l=t.value;n.payload=function(){return r(l)},n.callback=function(){ql(e,t)}}var i=e.stateNode;return i!==null&&typeof i.componentDidCatch=="function"&&(n.callback=function(){ql(e,t),typeof r!="function"&&(bt===null?bt=new Set([this]):bt.add(this));var o=t.stack;this.componentDidCatch(t.value,{componentStack:o!==null?o:""})}),n}function na(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new nf;var l=new Set;r.set(t,l)}else l=r.get(t),l===void 0&&(l=new Set,r.set(t,l));l.has(n)||(l.add(n),e=xf.bind(null,e,t,n),t.then(e,e))}function ra(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function sa(e,t,n,r,l){return e.mode&1?(e.flags|=65536,e.lanes=l,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=lt(-1,1),t.tag=2,kt(n,t,1))),n.lanes|=1),e)}var rf=ut.ReactCurrentOwner,we=!1;function he(e,t,n,r){t.child=e===null?Hc(t,null,n,r):Sn(t,e.child,n,r)}function la(e,t,n,r,l){n=n.render;var i=t.ref;return yn(t,l),r=Gi(e,t,n,r,i,l),n=qi(),e!==null&&!we?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~l,ct(e,t,l)):(q&&n&&zi(t),t.flags|=1,he(e,t,r,l),t.child)}function ia(e,t,n,r,l){if(e===null){var i=n.type;return typeof i=="function"&&!lo(i)&&i.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=i,hu(e,t,i,r,l)):(e=Xr(n.type,null,r,t,t.mode,l),e.ref=t.ref,e.return=t,t.child=e)}if(i=e.child,!(e.lanes&l)){var o=i.memoizedProps;if(n=n.compare,n=n!==null?n:ir,n(o,r)&&e.ref===t.ref)return ct(e,t,l)}return t.flags|=1,e=Et(i,r),e.ref=t.ref,e.return=t,t.child=e}function hu(e,t,n,r,l){if(e!==null){var i=e.memoizedProps;if(ir(i,r)&&e.ref===t.ref)if(we=!1,t.pendingProps=r=i,(e.lanes&l)!==0)e.flags&131072&&(we=!0);else return t.lanes=e.lanes,ct(e,t,l)}return Yl(e,t,n,r,l)}function gu(e,t,n){var r=t.pendingProps,l=r.children,i=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},W(fn,be),be|=n;else{if(!(n&1073741824))return e=i!==null?i.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,W(fn,be),be|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=i!==null?i.baseLanes:n,W(fn,be),be|=r}else i!==null?(r=i.baseLanes|n,t.memoizedState=null):r=n,W(fn,be),be|=r;return he(e,t,l,n),t.child}function xu(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function Yl(e,t,n,r,l){var i=Se(n)?Qt:pe.current;return i=wn(t,i),yn(t,l),n=Gi(e,t,n,r,i,l),r=qi(),e!==null&&!we?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~l,ct(e,t,l)):(q&&r&&zi(t),t.flags|=1,he(e,t,n,l),t.child)}function oa(e,t,n,r,l){if(Se(n)){var i=!0;cs(t)}else i=!1;if(yn(t,l),t.stateNode===null)Gr(e,t),mu(t,n,r),Gl(t,n,r,l),r=!0;else if(e===null){var o=t.stateNode,a=t.memoizedProps;o.props=a;var c=o.context,f=n.contextType;typeof f=="object"&&f!==null?f=ze(f):(f=Se(n)?Qt:pe.current,f=wn(t,f));var x=n.getDerivedStateFromProps,g=typeof x=="function"||typeof o.getSnapshotBeforeUpdate=="function";g||typeof o.UNSAFE_componentWillReceiveProps!="function"&&typeof o.componentWillReceiveProps!="function"||(a!==r||c!==f)&&ta(t,o,r,f),pt=!1;var y=t.memoizedState;o.state=y,ps(t,r,o,l),c=t.memoizedState,a!==r||y!==c||Ne.current||pt?(typeof x=="function"&&(Kl(t,n,x,r),c=t.memoizedState),(a=pt||ea(t,n,a,r,y,c,f))?(g||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount()),typeof o.componentDidMount=="function"&&(t.flags|=4194308)):(typeof o.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=c),o.props=r,o.state=c,o.context=f,r=a):(typeof o.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{o=t.stateNode,Wc(e,t),a=t.memoizedProps,f=t.type===t.elementType?a:Oe(t.type,a),o.props=f,g=t.pendingProps,y=o.context,c=n.contextType,typeof c=="object"&&c!==null?c=ze(c):(c=Se(n)?Qt:pe.current,c=wn(t,c));var v=n.getDerivedStateFromProps;(x=typeof v=="function"||typeof o.getSnapshotBeforeUpdate=="function")||typeof o.UNSAFE_componentWillReceiveProps!="function"&&typeof o.componentWillReceiveProps!="function"||(a!==g||y!==c)&&ta(t,o,r,c),pt=!1,y=t.memoizedState,o.state=y,ps(t,r,o,l);var w=t.memoizedState;a!==g||y!==w||Ne.current||pt?(typeof v=="function"&&(Kl(t,n,v,r),w=t.memoizedState),(f=pt||ea(t,n,f,r,y,w,c)||!1)?(x||typeof o.UNSAFE_componentWillUpdate!="function"&&typeof o.componentWillUpdate!="function"||(typeof o.componentWillUpdate=="function"&&o.componentWillUpdate(r,w,c),typeof o.UNSAFE_componentWillUpdate=="function"&&o.UNSAFE_componentWillUpdate(r,w,c)),typeof o.componentDidUpdate=="function"&&(t.flags|=4),typeof o.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof o.componentDidUpdate!="function"||a===e.memoizedProps&&y===e.memoizedState||(t.flags|=4),typeof o.getSnapshotBeforeUpdate!="function"||a===e.memoizedProps&&y===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=w),o.props=r,o.state=w,o.context=c,r=f):(typeof o.componentDidUpdate!="function"||a===e.memoizedProps&&y===e.memoizedState||(t.flags|=4),typeof o.getSnapshotBeforeUpdate!="function"||a===e.memoizedProps&&y===e.memoizedState||(t.flags|=1024),r=!1)}return Xl(e,t,n,r,i,l)}function Xl(e,t,n,r,l,i){xu(e,t);var o=(t.flags&128)!==0;if(!r&&!o)return l&&Qo(t,n,!1),ct(e,t,i);r=t.stateNode,rf.current=t;var a=o&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&o?(t.child=Sn(t,e.child,null,i),t.child=Sn(t,null,a,i)):he(e,t,a,i),t.memoizedState=r.state,l&&Qo(t,n,!0),t.child}function yu(e){var t=e.stateNode;t.pendingContext?Wo(e,t.pendingContext,t.pendingContext!==t.context):t.context&&Wo(e,t.context,!1),Bi(e,t.containerInfo)}function aa(e,t,n,r,l){return Nn(),$i(l),t.flags|=256,he(e,t,n,r),t.child}var Zl={dehydrated:null,treeContext:null,retryLane:0};function Jl(e){return{baseLanes:e,cachePool:null,transitions:null}}function vu(e,t,n){var r=t.pendingProps,l=Y.current,i=!1,o=(t.flags&128)!==0,a;if((a=o)||(a=e!==null&&e.memoizedState===null?!1:(l&2)!==0),a?(i=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(l|=1),W(Y,l&1),e===null)return Wl(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(o=r.children,e=r.fallback,i?(r=t.mode,i=t.child,o={mode:"hidden",children:o},!(r&1)&&i!==null?(i.childLanes=0,i.pendingProps=o):i=Rs(o,r,0,null),e=Wt(e,r,n,null),i.return=t,e.return=t,i.sibling=e,t.child=i,t.child.memoizedState=Jl(n),t.memoizedState=Zl,e):Zi(t,o));if(l=e.memoizedState,l!==null&&(a=l.dehydrated,a!==null))return sf(e,t,o,r,a,l,n);if(i){i=r.fallback,o=t.mode,l=e.child,a=l.sibling;var c={mode:"hidden",children:r.children};return!(o&1)&&t.child!==l?(r=t.child,r.childLanes=0,r.pendingProps=c,t.deletions=null):(r=Et(l,c),r.subtreeFlags=l.subtreeFlags&14680064),a!==null?i=Et(a,i):(i=Wt(i,o,n,null),i.flags|=2),i.return=t,r.return=t,r.sibling=i,t.child=r,r=i,i=t.child,o=e.child.memoizedState,o=o===null?Jl(n):{baseLanes:o.baseLanes|n,cachePool:null,transitions:o.transitions},i.memoizedState=o,i.childLanes=e.childLanes&~n,t.memoizedState=Zl,r}return i=e.child,e=i.sibling,r=Et(i,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function Zi(e,t){return t=Rs({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function Pr(e,t,n,r){return r!==null&&$i(r),Sn(t,e.child,null,n),e=Zi(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function sf(e,t,n,r,l,i,o){if(n)return t.flags&256?(t.flags&=-257,r=ul(Error(k(422))),Pr(e,t,o,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(i=r.fallback,l=t.mode,r=Rs({mode:"visible",children:r.children},l,0,null),i=Wt(i,l,o,null),i.flags|=2,r.return=t,i.return=t,r.sibling=i,t.child=r,t.mode&1&&Sn(t,e.child,null,o),t.child.memoizedState=Jl(o),t.memoizedState=Zl,i);if(!(t.mode&1))return Pr(e,t,o,null);if(l.data==="$!"){if(r=l.nextSibling&&l.nextSibling.dataset,r)var a=r.dgst;return r=a,i=Error(k(419)),r=ul(i,r,void 0),Pr(e,t,o,r)}if(a=(o&e.childLanes)!==0,we||a){if(r=ie,r!==null){switch(o&-o){case 4:l=2;break;case 16:l=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:l=32;break;case 536870912:l=268435456;break;default:l=0}l=l&(r.suspendedLanes|o)?0:l,l!==0&&l!==i.retryLane&&(i.retryLane=l,at(e,l),Be(r,e,l,-1))}return so(),r=ul(Error(k(421))),Pr(e,t,o,r)}return l.data==="$?"?(t.flags|=128,t.child=e.child,t=yf.bind(null,e),l._reactRetry=t,null):(e=i.treeContext,Ce=St(l.nextSibling),Ee=t,q=!0,Fe=null,e!==null&&(Ae[Ie++]=nt,Ae[Ie++]=rt,Ae[Ie++]=Kt,nt=e.id,rt=e.overflow,Kt=t),t=Zi(t,r.children),t.flags|=4096,t)}function ca(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),Ql(e.return,t,n)}function dl(e,t,n,r,l){var i=e.memoizedState;i===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:l}:(i.isBackwards=t,i.rendering=null,i.renderingStartTime=0,i.last=r,i.tail=n,i.tailMode=l)}function ju(e,t,n){var r=t.pendingProps,l=r.revealOrder,i=r.tail;if(he(e,t,r.children,n),r=Y.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&ca(e,n,t);else if(e.tag===19)ca(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(W(Y,r),!(t.mode&1))t.memoizedState=null;else switch(l){case"forwards":for(n=t.child,l=null;n!==null;)e=n.alternate,e!==null&&hs(e)===null&&(l=n),n=n.sibling;n=l,n===null?(l=t.child,t.child=null):(l=n.sibling,n.sibling=null),dl(t,!1,l,n,i);break;case"backwards":for(n=null,l=t.child,t.child=null;l!==null;){if(e=l.alternate,e!==null&&hs(e)===null){t.child=l;break}e=l.sibling,l.sibling=n,n=l,l=e}dl(t,!0,n,null,i);break;case"together":dl(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Gr(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function ct(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),qt|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(k(153));if(t.child!==null){for(e=t.child,n=Et(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=Et(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function lf(e,t,n){switch(t.tag){case 3:yu(t),Nn();break;case 5:Qc(t);break;case 1:Se(t.type)&&cs(t);break;case 4:Bi(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,l=t.memoizedProps.value;W(ms,r._currentValue),r._currentValue=l;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(W(Y,Y.current&1),t.flags|=128,null):n&t.child.childLanes?vu(e,t,n):(W(Y,Y.current&1),e=ct(e,t,n),e!==null?e.sibling:null);W(Y,Y.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return ju(e,t,n);t.flags|=128}if(l=t.memoizedState,l!==null&&(l.rendering=null,l.tail=null,l.lastEffect=null),W(Y,Y.current),r)break;return null;case 22:case 23:return t.lanes=0,gu(e,t,n)}return ct(e,t,n)}var wu,ei,Nu,Su;wu=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};ei=function(){};Nu=function(e,t,n,r){var l=e.memoizedProps;if(l!==r){e=t.stateNode,Ht(Xe.current);var i=null;switch(n){case"input":l=Nl(e,l),r=Nl(e,r),i=[];break;case"select":l=Z({},l,{value:void 0}),r=Z({},r,{value:void 0}),i=[];break;case"textarea":l=bl(e,l),r=bl(e,r),i=[];break;default:typeof l.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=os)}El(n,r);var o;n=null;for(f in l)if(!r.hasOwnProperty(f)&&l.hasOwnProperty(f)&&l[f]!=null)if(f==="style"){var a=l[f];for(o in a)a.hasOwnProperty(o)&&(n||(n={}),n[o]="")}else f!=="dangerouslySetInnerHTML"&&f!=="children"&&f!=="suppressContentEditableWarning"&&f!=="suppressHydrationWarning"&&f!=="autoFocus"&&(Jn.hasOwnProperty(f)?i||(i=[]):(i=i||[]).push(f,null));for(f in r){var c=r[f];if(a=l!=null?l[f]:void 0,r.hasOwnProperty(f)&&c!==a&&(c!=null||a!=null))if(f==="style")if(a){for(o in a)!a.hasOwnProperty(o)||c&&c.hasOwnProperty(o)||(n||(n={}),n[o]="");for(o in c)c.hasOwnProperty(o)&&a[o]!==c[o]&&(n||(n={}),n[o]=c[o])}else n||(i||(i=[]),i.push(f,n)),n=c;else f==="dangerouslySetInnerHTML"?(c=c?c.__html:void 0,a=a?a.__html:void 0,c!=null&&a!==c&&(i=i||[]).push(f,c)):f==="children"?typeof c!="string"&&typeof c!="number"||(i=i||[]).push(f,""+c):f!=="suppressContentEditableWarning"&&f!=="suppressHydrationWarning"&&(Jn.hasOwnProperty(f)?(c!=null&&f==="onScroll"&&K("scroll",e),i||a===c||(i=[])):(i=i||[]).push(f,c))}n&&(i=i||[]).push("style",n);var f=i;(t.updateQueue=f)&&(t.flags|=4)}};Su=function(e,t,n,r){n!==r&&(t.flags|=4)};function $n(e,t){if(!q)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function me(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var l=e.child;l!==null;)n|=l.lanes|l.childLanes,r|=l.subtreeFlags&14680064,r|=l.flags&14680064,l.return=e,l=l.sibling;else for(l=e.child;l!==null;)n|=l.lanes|l.childLanes,r|=l.subtreeFlags,r|=l.flags,l.return=e,l=l.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function of(e,t,n){var r=t.pendingProps;switch(Ri(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return me(t),null;case 1:return Se(t.type)&&as(),me(t),null;case 3:return r=t.stateNode,kn(),G(Ne),G(pe),Qi(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(Ir(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,Fe!==null&&(ai(Fe),Fe=null))),ei(e,t),me(t),null;case 5:Wi(t);var l=Ht(dr.current);if(n=t.type,e!==null&&t.stateNode!=null)Nu(e,t,n,r,l),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(k(166));return me(t),null}if(e=Ht(Xe.current),Ir(t)){r=t.stateNode,n=t.type;var i=t.memoizedProps;switch(r[qe]=t,r[cr]=i,e=(t.mode&1)!==0,n){case"dialog":K("cancel",r),K("close",r);break;case"iframe":case"object":case"embed":K("load",r);break;case"video":case"audio":for(l=0;l<Hn.length;l++)K(Hn[l],r);break;case"source":K("error",r);break;case"img":case"image":case"link":K("error",r),K("load",r);break;case"details":K("toggle",r);break;case"input":yo(r,i),K("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!i.multiple},K("invalid",r);break;case"textarea":jo(r,i),K("invalid",r)}El(n,i),l=null;for(var o in i)if(i.hasOwnProperty(o)){var a=i[o];o==="children"?typeof a=="string"?r.textContent!==a&&(i.suppressHydrationWarning!==!0&&Ar(r.textContent,a,e),l=["children",a]):typeof a=="number"&&r.textContent!==""+a&&(i.suppressHydrationWarning!==!0&&Ar(r.textContent,a,e),l=["children",""+a]):Jn.hasOwnProperty(o)&&a!=null&&o==="onScroll"&&K("scroll",r)}switch(n){case"input":kr(r),vo(r,i,!0);break;case"textarea":kr(r),wo(r);break;case"select":case"option":break;default:typeof i.onClick=="function"&&(r.onclick=os)}r=l,t.updateQueue=r,r!==null&&(t.flags|=4)}else{o=l.nodeType===9?l:l.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=Xa(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=o.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=o.createElement(n,{is:r.is}):(e=o.createElement(n),n==="select"&&(o=e,r.multiple?o.multiple=!0:r.size&&(o.size=r.size))):e=o.createElementNS(e,n),e[qe]=t,e[cr]=r,wu(e,t,!1,!1),t.stateNode=e;e:{switch(o=Ll(n,r),n){case"dialog":K("cancel",e),K("close",e),l=r;break;case"iframe":case"object":case"embed":K("load",e),l=r;break;case"video":case"audio":for(l=0;l<Hn.length;l++)K(Hn[l],e);l=r;break;case"source":K("error",e),l=r;break;case"img":case"image":case"link":K("error",e),K("load",e),l=r;break;case"details":K("toggle",e),l=r;break;case"input":yo(e,r),l=Nl(e,r),K("invalid",e);break;case"option":l=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},l=Z({},r,{value:void 0}),K("invalid",e);break;case"textarea":jo(e,r),l=bl(e,r),K("invalid",e);break;default:l=r}El(n,l),a=l;for(i in a)if(a.hasOwnProperty(i)){var c=a[i];i==="style"?ec(e,c):i==="dangerouslySetInnerHTML"?(c=c?c.__html:void 0,c!=null&&Za(e,c)):i==="children"?typeof c=="string"?(n!=="textarea"||c!=="")&&er(e,c):typeof c=="number"&&er(e,""+c):i!=="suppressContentEditableWarning"&&i!=="suppressHydrationWarning"&&i!=="autoFocus"&&(Jn.hasOwnProperty(i)?c!=null&&i==="onScroll"&&K("scroll",e):c!=null&&Ni(e,i,c,o))}switch(n){case"input":kr(e),vo(e,r,!1);break;case"textarea":kr(e),wo(e);break;case"option":r.value!=null&&e.setAttribute("value",""+Dt(r.value));break;case"select":e.multiple=!!r.multiple,i=r.value,i!=null?pn(e,!!r.multiple,i,!1):r.defaultValue!=null&&pn(e,!!r.multiple,r.defaultValue,!0);break;default:typeof l.onClick=="function"&&(e.onclick=os)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return me(t),null;case 6:if(e&&t.stateNode!=null)Su(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(k(166));if(n=Ht(dr.current),Ht(Xe.current),Ir(t)){if(r=t.stateNode,n=t.memoizedProps,r[qe]=t,(i=r.nodeValue!==n)&&(e=Ee,e!==null))switch(e.tag){case 3:Ar(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&Ar(r.nodeValue,n,(e.mode&1)!==0)}i&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[qe]=t,t.stateNode=r}return me(t),null;case 13:if(G(Y),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(q&&Ce!==null&&t.mode&1&&!(t.flags&128))Fc(),Nn(),t.flags|=98560,i=!1;else if(i=Ir(t),r!==null&&r.dehydrated!==null){if(e===null){if(!i)throw Error(k(318));if(i=t.memoizedState,i=i!==null?i.dehydrated:null,!i)throw Error(k(317));i[qe]=t}else Nn(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;me(t),i=!1}else Fe!==null&&(ai(Fe),Fe=null),i=!0;if(!i)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||Y.current&1?se===0&&(se=3):so())),t.updateQueue!==null&&(t.flags|=4),me(t),null);case 4:return kn(),ei(e,t),e===null&&or(t.stateNode.containerInfo),me(t),null;case 10:return Fi(t.type._context),me(t),null;case 17:return Se(t.type)&&as(),me(t),null;case 19:if(G(Y),i=t.memoizedState,i===null)return me(t),null;if(r=(t.flags&128)!==0,o=i.rendering,o===null)if(r)$n(i,!1);else{if(se!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(o=hs(e),o!==null){for(t.flags|=128,$n(i,!1),r=o.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)i=n,e=r,i.flags&=14680066,o=i.alternate,o===null?(i.childLanes=0,i.lanes=e,i.child=null,i.subtreeFlags=0,i.memoizedProps=null,i.memoizedState=null,i.updateQueue=null,i.dependencies=null,i.stateNode=null):(i.childLanes=o.childLanes,i.lanes=o.lanes,i.child=o.child,i.subtreeFlags=0,i.deletions=null,i.memoizedProps=o.memoizedProps,i.memoizedState=o.memoizedState,i.updateQueue=o.updateQueue,i.type=o.type,e=o.dependencies,i.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return W(Y,Y.current&1|2),t.child}e=e.sibling}i.tail!==null&&te()>Cn&&(t.flags|=128,r=!0,$n(i,!1),t.lanes=4194304)}else{if(!r)if(e=hs(o),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),$n(i,!0),i.tail===null&&i.tailMode==="hidden"&&!o.alternate&&!q)return me(t),null}else 2*te()-i.renderingStartTime>Cn&&n!==1073741824&&(t.flags|=128,r=!0,$n(i,!1),t.lanes=4194304);i.isBackwards?(o.sibling=t.child,t.child=o):(n=i.last,n!==null?n.sibling=o:t.child=o,i.last=o)}return i.tail!==null?(t=i.tail,i.rendering=t,i.tail=t.sibling,i.renderingStartTime=te(),t.sibling=null,n=Y.current,W(Y,r?n&1|2:n&1),t):(me(t),null);case 22:case 23:return ro(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?be&1073741824&&(me(t),t.subtreeFlags&6&&(t.flags|=8192)):me(t),null;case 24:return null;case 25:return null}throw Error(k(156,t.tag))}function af(e,t){switch(Ri(t),t.tag){case 1:return Se(t.type)&&as(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return kn(),G(Ne),G(pe),Qi(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return Wi(t),null;case 13:if(G(Y),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(k(340));Nn()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return G(Y),null;case 4:return kn(),null;case 10:return Fi(t.type._context),null;case 22:case 23:return ro(),null;case 24:return null;default:return null}}var zr=!1,fe=!1,cf=typeof WeakSet=="function"?WeakSet:Set,E=null;function mn(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){J(e,t,r)}else n.current=null}function ti(e,t,n){try{n()}catch(r){J(e,t,r)}}var ua=!1;function uf(e,t){if($l=ss,e=Lc(),Pi(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var l=r.anchorOffset,i=r.focusNode;r=r.focusOffset;try{n.nodeType,i.nodeType}catch{n=null;break e}var o=0,a=-1,c=-1,f=0,x=0,g=e,y=null;t:for(;;){for(var v;g!==n||l!==0&&g.nodeType!==3||(a=o+l),g!==i||r!==0&&g.nodeType!==3||(c=o+r),g.nodeType===3&&(o+=g.nodeValue.length),(v=g.firstChild)!==null;)y=g,g=v;for(;;){if(g===e)break t;if(y===n&&++f===l&&(a=o),y===i&&++x===r&&(c=o),(v=g.nextSibling)!==null)break;g=y,y=g.parentNode}g=v}n=a===-1||c===-1?null:{start:a,end:c}}else n=null}n=n||{start:0,end:0}}else n=null;for(Ol={focusedElem:e,selectionRange:n},ss=!1,E=t;E!==null;)if(t=E,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,E=e;else for(;E!==null;){t=E;try{var w=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(w!==null){var h=w.memoizedProps,N=w.memoizedState,m=t.stateNode,d=m.getSnapshotBeforeUpdate(t.elementType===t.type?h:Oe(t.type,h),N);m.__reactInternalSnapshotBeforeUpdate=d}break;case 3:var u=t.stateNode.containerInfo;u.nodeType===1?u.textContent="":u.nodeType===9&&u.documentElement&&u.removeChild(u.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(k(163))}}catch(p){J(t,t.return,p)}if(e=t.sibling,e!==null){e.return=t.return,E=e;break}E=t.return}return w=ua,ua=!1,w}function Yn(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var l=r=r.next;do{if((l.tag&e)===e){var i=l.destroy;l.destroy=void 0,i!==void 0&&ti(t,n,i)}l=l.next}while(l!==r)}}function Ps(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function ni(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function ku(e){var t=e.alternate;t!==null&&(e.alternate=null,ku(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[qe],delete t[cr],delete t[Vl],delete t[Wm],delete t[Qm])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function bu(e){return e.tag===5||e.tag===3||e.tag===4}function da(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||bu(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function ri(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=os));else if(r!==4&&(e=e.child,e!==null))for(ri(e,t,n),e=e.sibling;e!==null;)ri(e,t,n),e=e.sibling}function si(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(si(e,t,n),e=e.sibling;e!==null;)si(e,t,n),e=e.sibling}var ae=null,Ue=!1;function mt(e,t,n){for(n=n.child;n!==null;)Cu(e,t,n),n=n.sibling}function Cu(e,t,n){if(Ye&&typeof Ye.onCommitFiberUnmount=="function")try{Ye.onCommitFiberUnmount(Es,n)}catch{}switch(n.tag){case 5:fe||mn(n,t);case 6:var r=ae,l=Ue;ae=null,mt(e,t,n),ae=r,Ue=l,ae!==null&&(Ue?(e=ae,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):ae.removeChild(n.stateNode));break;case 18:ae!==null&&(Ue?(e=ae,n=n.stateNode,e.nodeType===8?sl(e.parentNode,n):e.nodeType===1&&sl(e,n),sr(e)):sl(ae,n.stateNode));break;case 4:r=ae,l=Ue,ae=n.stateNode.containerInfo,Ue=!0,mt(e,t,n),ae=r,Ue=l;break;case 0:case 11:case 14:case 15:if(!fe&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){l=r=r.next;do{var i=l,o=i.destroy;i=i.tag,o!==void 0&&(i&2||i&4)&&ti(n,t,o),l=l.next}while(l!==r)}mt(e,t,n);break;case 1:if(!fe&&(mn(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(a){J(n,t,a)}mt(e,t,n);break;case 21:mt(e,t,n);break;case 22:n.mode&1?(fe=(r=fe)||n.memoizedState!==null,mt(e,t,n),fe=r):mt(e,t,n);break;default:mt(e,t,n)}}function ma(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new cf),t.forEach(function(r){var l=vf.bind(null,e,r);n.has(r)||(n.add(r),r.then(l,l))})}}function $e(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var l=n[r];try{var i=e,o=t,a=o;e:for(;a!==null;){switch(a.tag){case 5:ae=a.stateNode,Ue=!1;break e;case 3:ae=a.stateNode.containerInfo,Ue=!0;break e;case 4:ae=a.stateNode.containerInfo,Ue=!0;break e}a=a.return}if(ae===null)throw Error(k(160));Cu(i,o,l),ae=null,Ue=!1;var c=l.alternate;c!==null&&(c.return=null),l.return=null}catch(f){J(l,t,f)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)Eu(t,e),t=t.sibling}function Eu(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if($e(t,e),Ke(e),r&4){try{Yn(3,e,e.return),Ps(3,e)}catch(h){J(e,e.return,h)}try{Yn(5,e,e.return)}catch(h){J(e,e.return,h)}}break;case 1:$e(t,e),Ke(e),r&512&&n!==null&&mn(n,n.return);break;case 5:if($e(t,e),Ke(e),r&512&&n!==null&&mn(n,n.return),e.flags&32){var l=e.stateNode;try{er(l,"")}catch(h){J(e,e.return,h)}}if(r&4&&(l=e.stateNode,l!=null)){var i=e.memoizedProps,o=n!==null?n.memoizedProps:i,a=e.type,c=e.updateQueue;if(e.updateQueue=null,c!==null)try{a==="input"&&i.type==="radio"&&i.name!=null&&qa(l,i),Ll(a,o);var f=Ll(a,i);for(o=0;o<c.length;o+=2){var x=c[o],g=c[o+1];x==="style"?ec(l,g):x==="dangerouslySetInnerHTML"?Za(l,g):x==="children"?er(l,g):Ni(l,x,g,f)}switch(a){case"input":Sl(l,i);break;case"textarea":Ya(l,i);break;case"select":var y=l._wrapperState.wasMultiple;l._wrapperState.wasMultiple=!!i.multiple;var v=i.value;v!=null?pn(l,!!i.multiple,v,!1):y!==!!i.multiple&&(i.defaultValue!=null?pn(l,!!i.multiple,i.defaultValue,!0):pn(l,!!i.multiple,i.multiple?[]:"",!1))}l[cr]=i}catch(h){J(e,e.return,h)}}break;case 6:if($e(t,e),Ke(e),r&4){if(e.stateNode===null)throw Error(k(162));l=e.stateNode,i=e.memoizedProps;try{l.nodeValue=i}catch(h){J(e,e.return,h)}}break;case 3:if($e(t,e),Ke(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{sr(t.containerInfo)}catch(h){J(e,e.return,h)}break;case 4:$e(t,e),Ke(e);break;case 13:$e(t,e),Ke(e),l=e.child,l.flags&8192&&(i=l.memoizedState!==null,l.stateNode.isHidden=i,!i||l.alternate!==null&&l.alternate.memoizedState!==null||(to=te())),r&4&&ma(e);break;case 22:if(x=n!==null&&n.memoizedState!==null,e.mode&1?(fe=(f=fe)||x,$e(t,e),fe=f):$e(t,e),Ke(e),r&8192){if(f=e.memoizedState!==null,(e.stateNode.isHidden=f)&&!x&&e.mode&1)for(E=e,x=e.child;x!==null;){for(g=E=x;E!==null;){switch(y=E,v=y.child,y.tag){case 0:case 11:case 14:case 15:Yn(4,y,y.return);break;case 1:mn(y,y.return);var w=y.stateNode;if(typeof w.componentWillUnmount=="function"){r=y,n=y.return;try{t=r,w.props=t.memoizedProps,w.state=t.memoizedState,w.componentWillUnmount()}catch(h){J(r,n,h)}}break;case 5:mn(y,y.return);break;case 22:if(y.memoizedState!==null){pa(g);continue}}v!==null?(v.return=y,E=v):pa(g)}x=x.sibling}e:for(x=null,g=e;;){if(g.tag===5){if(x===null){x=g;try{l=g.stateNode,f?(i=l.style,typeof i.setProperty=="function"?i.setProperty("display","none","important"):i.display="none"):(a=g.stateNode,c=g.memoizedProps.style,o=c!=null&&c.hasOwnProperty("display")?c.display:null,a.style.display=Ja("display",o))}catch(h){J(e,e.return,h)}}}else if(g.tag===6){if(x===null)try{g.stateNode.nodeValue=f?"":g.memoizedProps}catch(h){J(e,e.return,h)}}else if((g.tag!==22&&g.tag!==23||g.memoizedState===null||g===e)&&g.child!==null){g.child.return=g,g=g.child;continue}if(g===e)break e;for(;g.sibling===null;){if(g.return===null||g.return===e)break e;x===g&&(x=null),g=g.return}x===g&&(x=null),g.sibling.return=g.return,g=g.sibling}}break;case 19:$e(t,e),Ke(e),r&4&&ma(e);break;case 21:break;default:$e(t,e),Ke(e)}}function Ke(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(bu(n)){var r=n;break e}n=n.return}throw Error(k(160))}switch(r.tag){case 5:var l=r.stateNode;r.flags&32&&(er(l,""),r.flags&=-33);var i=da(e);si(e,i,l);break;case 3:case 4:var o=r.stateNode.containerInfo,a=da(e);ri(e,a,o);break;default:throw Error(k(161))}}catch(c){J(e,e.return,c)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function df(e,t,n){E=e,Lu(e)}function Lu(e,t,n){for(var r=(e.mode&1)!==0;E!==null;){var l=E,i=l.child;if(l.tag===22&&r){var o=l.memoizedState!==null||zr;if(!o){var a=l.alternate,c=a!==null&&a.memoizedState!==null||fe;a=zr;var f=fe;if(zr=o,(fe=c)&&!f)for(E=l;E!==null;)o=E,c=o.child,o.tag===22&&o.memoizedState!==null?ha(l):c!==null?(c.return=o,E=c):ha(l);for(;i!==null;)E=i,Lu(i),i=i.sibling;E=l,zr=a,fe=f}fa(e)}else l.subtreeFlags&8772&&i!==null?(i.return=l,E=i):fa(e)}}function fa(e){for(;E!==null;){var t=E;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:fe||Ps(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!fe)if(n===null)r.componentDidMount();else{var l=t.elementType===t.type?n.memoizedProps:Oe(t.type,n.memoizedProps);r.componentDidUpdate(l,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var i=t.updateQueue;i!==null&&Xo(t,i,r);break;case 3:var o=t.updateQueue;if(o!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}Xo(t,o,n)}break;case 5:var a=t.stateNode;if(n===null&&t.flags&4){n=a;var c=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":c.autoFocus&&n.focus();break;case"img":c.src&&(n.src=c.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var f=t.alternate;if(f!==null){var x=f.memoizedState;if(x!==null){var g=x.dehydrated;g!==null&&sr(g)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(k(163))}fe||t.flags&512&&ni(t)}catch(y){J(t,t.return,y)}}if(t===e){E=null;break}if(n=t.sibling,n!==null){n.return=t.return,E=n;break}E=t.return}}function pa(e){for(;E!==null;){var t=E;if(t===e){E=null;break}var n=t.sibling;if(n!==null){n.return=t.return,E=n;break}E=t.return}}function ha(e){for(;E!==null;){var t=E;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{Ps(4,t)}catch(c){J(t,n,c)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var l=t.return;try{r.componentDidMount()}catch(c){J(t,l,c)}}var i=t.return;try{ni(t)}catch(c){J(t,i,c)}break;case 5:var o=t.return;try{ni(t)}catch(c){J(t,o,c)}}}catch(c){J(t,t.return,c)}if(t===e){E=null;break}var a=t.sibling;if(a!==null){a.return=t.return,E=a;break}E=t.return}}var mf=Math.ceil,ys=ut.ReactCurrentDispatcher,Ji=ut.ReactCurrentOwner,Pe=ut.ReactCurrentBatchConfig,U=0,ie=null,ne=null,ce=0,be=0,fn=It(0),se=0,hr=null,qt=0,zs=0,eo=0,Xn=null,je=null,to=0,Cn=1/0,et=null,vs=!1,li=null,bt=null,Rr=!1,yt=null,js=0,Zn=0,ii=null,qr=-1,Yr=0;function ge(){return U&6?te():qr!==-1?qr:qr=te()}function Ct(e){return e.mode&1?U&2&&ce!==0?ce&-ce:Gm.transition!==null?(Yr===0&&(Yr=mc()),Yr):(e=H,e!==0||(e=window.event,e=e===void 0?16:vc(e.type)),e):1}function Be(e,t,n,r){if(50<Zn)throw Zn=0,ii=null,Error(k(185));yr(e,n,r),(!(U&2)||e!==ie)&&(e===ie&&(!(U&2)&&(zs|=n),se===4&&gt(e,ce)),ke(e,r),n===1&&U===0&&!(t.mode&1)&&(Cn=te()+500,As&&_t()))}function ke(e,t){var n=e.callbackNode;Gd(e,t);var r=rs(e,e===ie?ce:0);if(r===0)n!==null&&ko(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&ko(n),t===1)e.tag===0?Km(ga.bind(null,e)):$c(ga.bind(null,e)),Hm(function(){!(U&6)&&_t()}),n=null;else{switch(fc(r)){case 1:n=Ei;break;case 4:n=uc;break;case 16:n=ns;break;case 536870912:n=dc;break;default:n=ns}n=zu(n,Tu.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function Tu(e,t){if(qr=-1,Yr=0,U&6)throw Error(k(327));var n=e.callbackNode;if(vn()&&e.callbackNode!==n)return null;var r=rs(e,e===ie?ce:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=ws(e,r);else{t=r;var l=U;U|=2;var i=Mu();(ie!==e||ce!==t)&&(et=null,Cn=te()+500,Bt(e,t));do try{hf();break}catch(a){Du(e,a)}while(1);Ui(),ys.current=i,U=l,ne!==null?t=0:(ie=null,ce=0,t=se)}if(t!==0){if(t===2&&(l=Il(e),l!==0&&(r=l,t=oi(e,l))),t===1)throw n=hr,Bt(e,0),gt(e,r),ke(e,te()),n;if(t===6)gt(e,r);else{if(l=e.current.alternate,!(r&30)&&!ff(l)&&(t=ws(e,r),t===2&&(i=Il(e),i!==0&&(r=i,t=oi(e,i))),t===1))throw n=hr,Bt(e,0),gt(e,r),ke(e,te()),n;switch(e.finishedWork=l,e.finishedLanes=r,t){case 0:case 1:throw Error(k(345));case 2:$t(e,je,et);break;case 3:if(gt(e,r),(r&130023424)===r&&(t=to+500-te(),10<t)){if(rs(e,0)!==0)break;if(l=e.suspendedLanes,(l&r)!==r){ge(),e.pingedLanes|=e.suspendedLanes&l;break}e.timeoutHandle=Fl($t.bind(null,e,je,et),t);break}$t(e,je,et);break;case 4:if(gt(e,r),(r&4194240)===r)break;for(t=e.eventTimes,l=-1;0<r;){var o=31-He(r);i=1<<o,o=t[o],o>l&&(l=o),r&=~i}if(r=l,r=te()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*mf(r/1960))-r,10<r){e.timeoutHandle=Fl($t.bind(null,e,je,et),r);break}$t(e,je,et);break;case 5:$t(e,je,et);break;default:throw Error(k(329))}}}return ke(e,te()),e.callbackNode===n?Tu.bind(null,e):null}function oi(e,t){var n=Xn;return e.current.memoizedState.isDehydrated&&(Bt(e,t).flags|=256),e=ws(e,t),e!==2&&(t=je,je=n,t!==null&&ai(t)),e}function ai(e){je===null?je=e:je.push.apply(je,e)}function ff(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var l=n[r],i=l.getSnapshot;l=l.value;try{if(!We(i(),l))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function gt(e,t){for(t&=~eo,t&=~zs,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-He(t),r=1<<n;e[n]=-1,t&=~r}}function ga(e){if(U&6)throw Error(k(327));vn();var t=rs(e,0);if(!(t&1))return ke(e,te()),null;var n=ws(e,t);if(e.tag!==0&&n===2){var r=Il(e);r!==0&&(t=r,n=oi(e,r))}if(n===1)throw n=hr,Bt(e,0),gt(e,t),ke(e,te()),n;if(n===6)throw Error(k(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,$t(e,je,et),ke(e,te()),null}function no(e,t){var n=U;U|=1;try{return e(t)}finally{U=n,U===0&&(Cn=te()+500,As&&_t())}}function Yt(e){yt!==null&&yt.tag===0&&!(U&6)&&vn();var t=U;U|=1;var n=Pe.transition,r=H;try{if(Pe.transition=null,H=1,e)return e()}finally{H=r,Pe.transition=n,U=t,!(U&6)&&_t()}}function ro(){be=fn.current,G(fn)}function Bt(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,Vm(n)),ne!==null)for(n=ne.return;n!==null;){var r=n;switch(Ri(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&as();break;case 3:kn(),G(Ne),G(pe),Qi();break;case 5:Wi(r);break;case 4:kn();break;case 13:G(Y);break;case 19:G(Y);break;case 10:Fi(r.type._context);break;case 22:case 23:ro()}n=n.return}if(ie=e,ne=e=Et(e.current,null),ce=be=t,se=0,hr=null,eo=zs=qt=0,je=Xn=null,Vt!==null){for(t=0;t<Vt.length;t++)if(n=Vt[t],r=n.interleaved,r!==null){n.interleaved=null;var l=r.next,i=n.pending;if(i!==null){var o=i.next;i.next=l,r.next=o}n.pending=r}Vt=null}return e}function Du(e,t){do{var n=ne;try{if(Ui(),Qr.current=xs,gs){for(var r=X.memoizedState;r!==null;){var l=r.queue;l!==null&&(l.pending=null),r=r.next}gs=!1}if(Gt=0,le=re=X=null,qn=!1,mr=0,Ji.current=null,n===null||n.return===null){se=1,hr=t,ne=null;break}e:{var i=e,o=n.return,a=n,c=t;if(t=ce,a.flags|=32768,c!==null&&typeof c=="object"&&typeof c.then=="function"){var f=c,x=a,g=x.tag;if(!(x.mode&1)&&(g===0||g===11||g===15)){var y=x.alternate;y?(x.updateQueue=y.updateQueue,x.memoizedState=y.memoizedState,x.lanes=y.lanes):(x.updateQueue=null,x.memoizedState=null)}var v=ra(o);if(v!==null){v.flags&=-257,sa(v,o,a,i,t),v.mode&1&&na(i,f,t),t=v,c=f;var w=t.updateQueue;if(w===null){var h=new Set;h.add(c),t.updateQueue=h}else w.add(c);break e}else{if(!(t&1)){na(i,f,t),so();break e}c=Error(k(426))}}else if(q&&a.mode&1){var N=ra(o);if(N!==null){!(N.flags&65536)&&(N.flags|=256),sa(N,o,a,i,t),$i(bn(c,a));break e}}i=c=bn(c,a),se!==4&&(se=2),Xn===null?Xn=[i]:Xn.push(i),i=o;do{switch(i.tag){case 3:i.flags|=65536,t&=-t,i.lanes|=t;var m=fu(i,c,t);Yo(i,m);break e;case 1:a=c;var d=i.type,u=i.stateNode;if(!(i.flags&128)&&(typeof d.getDerivedStateFromError=="function"||u!==null&&typeof u.componentDidCatch=="function"&&(bt===null||!bt.has(u)))){i.flags|=65536,t&=-t,i.lanes|=t;var p=pu(i,a,t);Yo(i,p);break e}}i=i.return}while(i!==null)}Iu(n)}catch(j){t=j,ne===n&&n!==null&&(ne=n=n.return);continue}break}while(1)}function Mu(){var e=ys.current;return ys.current=xs,e===null?xs:e}function so(){(se===0||se===3||se===2)&&(se=4),ie===null||!(qt&268435455)&&!(zs&268435455)||gt(ie,ce)}function ws(e,t){var n=U;U|=2;var r=Mu();(ie!==e||ce!==t)&&(et=null,Bt(e,t));do try{pf();break}catch(l){Du(e,l)}while(1);if(Ui(),U=n,ys.current=r,ne!==null)throw Error(k(261));return ie=null,ce=0,se}function pf(){for(;ne!==null;)Au(ne)}function hf(){for(;ne!==null&&!Od();)Au(ne)}function Au(e){var t=Pu(e.alternate,e,be);e.memoizedProps=e.pendingProps,t===null?Iu(e):ne=t,Ji.current=null}function Iu(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=af(n,t),n!==null){n.flags&=32767,ne=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{se=6,ne=null;return}}else if(n=of(n,t,be),n!==null){ne=n;return}if(t=t.sibling,t!==null){ne=t;return}ne=t=e}while(t!==null);se===0&&(se=5)}function $t(e,t,n){var r=H,l=Pe.transition;try{Pe.transition=null,H=1,gf(e,t,n,r)}finally{Pe.transition=l,H=r}return null}function gf(e,t,n,r){do vn();while(yt!==null);if(U&6)throw Error(k(327));n=e.finishedWork;var l=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(k(177));e.callbackNode=null,e.callbackPriority=0;var i=n.lanes|n.childLanes;if(qd(e,i),e===ie&&(ne=ie=null,ce=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||Rr||(Rr=!0,zu(ns,function(){return vn(),null})),i=(n.flags&15990)!==0,n.subtreeFlags&15990||i){i=Pe.transition,Pe.transition=null;var o=H;H=1;var a=U;U|=4,Ji.current=null,uf(e,n),Eu(n,e),Pm(Ol),ss=!!$l,Ol=$l=null,e.current=n,df(n),Ud(),U=a,H=o,Pe.transition=i}else e.current=n;if(Rr&&(Rr=!1,yt=e,js=l),i=e.pendingLanes,i===0&&(bt=null),Hd(n.stateNode),ke(e,te()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)l=t[n],r(l.value,{componentStack:l.stack,digest:l.digest});if(vs)throw vs=!1,e=li,li=null,e;return js&1&&e.tag!==0&&vn(),i=e.pendingLanes,i&1?e===ii?Zn++:(Zn=0,ii=e):Zn=0,_t(),null}function vn(){if(yt!==null){var e=fc(js),t=Pe.transition,n=H;try{if(Pe.transition=null,H=16>e?16:e,yt===null)var r=!1;else{if(e=yt,yt=null,js=0,U&6)throw Error(k(331));var l=U;for(U|=4,E=e.current;E!==null;){var i=E,o=i.child;if(E.flags&16){var a=i.deletions;if(a!==null){for(var c=0;c<a.length;c++){var f=a[c];for(E=f;E!==null;){var x=E;switch(x.tag){case 0:case 11:case 15:Yn(8,x,i)}var g=x.child;if(g!==null)g.return=x,E=g;else for(;E!==null;){x=E;var y=x.sibling,v=x.return;if(ku(x),x===f){E=null;break}if(y!==null){y.return=v,E=y;break}E=v}}}var w=i.alternate;if(w!==null){var h=w.child;if(h!==null){w.child=null;do{var N=h.sibling;h.sibling=null,h=N}while(h!==null)}}E=i}}if(i.subtreeFlags&2064&&o!==null)o.return=i,E=o;else e:for(;E!==null;){if(i=E,i.flags&2048)switch(i.tag){case 0:case 11:case 15:Yn(9,i,i.return)}var m=i.sibling;if(m!==null){m.return=i.return,E=m;break e}E=i.return}}var d=e.current;for(E=d;E!==null;){o=E;var u=o.child;if(o.subtreeFlags&2064&&u!==null)u.return=o,E=u;else e:for(o=d;E!==null;){if(a=E,a.flags&2048)try{switch(a.tag){case 0:case 11:case 15:Ps(9,a)}}catch(j){J(a,a.return,j)}if(a===o){E=null;break e}var p=a.sibling;if(p!==null){p.return=a.return,E=p;break e}E=a.return}}if(U=l,_t(),Ye&&typeof Ye.onPostCommitFiberRoot=="function")try{Ye.onPostCommitFiberRoot(Es,e)}catch{}r=!0}return r}finally{H=n,Pe.transition=t}}return!1}function xa(e,t,n){t=bn(n,t),t=fu(e,t,1),e=kt(e,t,1),t=ge(),e!==null&&(yr(e,1,t),ke(e,t))}function J(e,t,n){if(e.tag===3)xa(e,e,n);else for(;t!==null;){if(t.tag===3){xa(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(bt===null||!bt.has(r))){e=bn(n,e),e=pu(t,e,1),t=kt(t,e,1),e=ge(),t!==null&&(yr(t,1,e),ke(t,e));break}}t=t.return}}function xf(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=ge(),e.pingedLanes|=e.suspendedLanes&n,ie===e&&(ce&n)===n&&(se===4||se===3&&(ce&130023424)===ce&&500>te()-to?Bt(e,0):eo|=n),ke(e,t)}function _u(e,t){t===0&&(e.mode&1?(t=Er,Er<<=1,!(Er&130023424)&&(Er=4194304)):t=1);var n=ge();e=at(e,t),e!==null&&(yr(e,t,n),ke(e,n))}function yf(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),_u(e,n)}function vf(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,l=e.memoizedState;l!==null&&(n=l.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(k(314))}r!==null&&r.delete(t),_u(e,n)}var Pu;Pu=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||Ne.current)we=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return we=!1,lf(e,t,n);we=!!(e.flags&131072)}else we=!1,q&&t.flags&1048576&&Oc(t,ds,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;Gr(e,t),e=t.pendingProps;var l=wn(t,pe.current);yn(t,n),l=Gi(null,t,r,e,l,n);var i=qi();return t.flags|=1,typeof l=="object"&&l!==null&&typeof l.render=="function"&&l.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Se(r)?(i=!0,cs(t)):i=!1,t.memoizedState=l.state!==null&&l.state!==void 0?l.state:null,Hi(t),l.updater=_s,t.stateNode=l,l._reactInternals=t,Gl(t,r,e,n),t=Xl(null,t,r,!0,i,n)):(t.tag=0,q&&i&&zi(t),he(null,t,l,n),t=t.child),t;case 16:r=t.elementType;e:{switch(Gr(e,t),e=t.pendingProps,l=r._init,r=l(r._payload),t.type=r,l=t.tag=wf(r),e=Oe(r,e),l){case 0:t=Yl(null,t,r,e,n);break e;case 1:t=oa(null,t,r,e,n);break e;case 11:t=la(null,t,r,e,n);break e;case 14:t=ia(null,t,r,Oe(r.type,e),n);break e}throw Error(k(306,r,""))}return t;case 0:return r=t.type,l=t.pendingProps,l=t.elementType===r?l:Oe(r,l),Yl(e,t,r,l,n);case 1:return r=t.type,l=t.pendingProps,l=t.elementType===r?l:Oe(r,l),oa(e,t,r,l,n);case 3:e:{if(yu(t),e===null)throw Error(k(387));r=t.pendingProps,i=t.memoizedState,l=i.element,Wc(e,t),ps(t,r,null,n);var o=t.memoizedState;if(r=o.element,i.isDehydrated)if(i={element:r,isDehydrated:!1,cache:o.cache,pendingSuspenseBoundaries:o.pendingSuspenseBoundaries,transitions:o.transitions},t.updateQueue.baseState=i,t.memoizedState=i,t.flags&256){l=bn(Error(k(423)),t),t=aa(e,t,r,n,l);break e}else if(r!==l){l=bn(Error(k(424)),t),t=aa(e,t,r,n,l);break e}else for(Ce=St(t.stateNode.containerInfo.firstChild),Ee=t,q=!0,Fe=null,n=Hc(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(Nn(),r===l){t=ct(e,t,n);break e}he(e,t,r,n)}t=t.child}return t;case 5:return Qc(t),e===null&&Wl(t),r=t.type,l=t.pendingProps,i=e!==null?e.memoizedProps:null,o=l.children,Ul(r,l)?o=null:i!==null&&Ul(r,i)&&(t.flags|=32),xu(e,t),he(e,t,o,n),t.child;case 6:return e===null&&Wl(t),null;case 13:return vu(e,t,n);case 4:return Bi(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=Sn(t,null,r,n):he(e,t,r,n),t.child;case 11:return r=t.type,l=t.pendingProps,l=t.elementType===r?l:Oe(r,l),la(e,t,r,l,n);case 7:return he(e,t,t.pendingProps,n),t.child;case 8:return he(e,t,t.pendingProps.children,n),t.child;case 12:return he(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,l=t.pendingProps,i=t.memoizedProps,o=l.value,W(ms,r._currentValue),r._currentValue=o,i!==null)if(We(i.value,o)){if(i.children===l.children&&!Ne.current){t=ct(e,t,n);break e}}else for(i=t.child,i!==null&&(i.return=t);i!==null;){var a=i.dependencies;if(a!==null){o=i.child;for(var c=a.firstContext;c!==null;){if(c.context===r){if(i.tag===1){c=lt(-1,n&-n),c.tag=2;var f=i.updateQueue;if(f!==null){f=f.shared;var x=f.pending;x===null?c.next=c:(c.next=x.next,x.next=c),f.pending=c}}i.lanes|=n,c=i.alternate,c!==null&&(c.lanes|=n),Ql(i.return,n,t),a.lanes|=n;break}c=c.next}}else if(i.tag===10)o=i.type===t.type?null:i.child;else if(i.tag===18){if(o=i.return,o===null)throw Error(k(341));o.lanes|=n,a=o.alternate,a!==null&&(a.lanes|=n),Ql(o,n,t),o=i.sibling}else o=i.child;if(o!==null)o.return=i;else for(o=i;o!==null;){if(o===t){o=null;break}if(i=o.sibling,i!==null){i.return=o.return,o=i;break}o=o.return}i=o}he(e,t,l.children,n),t=t.child}return t;case 9:return l=t.type,r=t.pendingProps.children,yn(t,n),l=ze(l),r=r(l),t.flags|=1,he(e,t,r,n),t.child;case 14:return r=t.type,l=Oe(r,t.pendingProps),l=Oe(r.type,l),ia(e,t,r,l,n);case 15:return hu(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,l=t.pendingProps,l=t.elementType===r?l:Oe(r,l),Gr(e,t),t.tag=1,Se(r)?(e=!0,cs(t)):e=!1,yn(t,n),mu(t,r,l),Gl(t,r,l,n),Xl(null,t,r,!0,e,n);case 19:return ju(e,t,n);case 22:return gu(e,t,n)}throw Error(k(156,t.tag))};function zu(e,t){return cc(e,t)}function jf(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function _e(e,t,n,r){return new jf(e,t,n,r)}function lo(e){return e=e.prototype,!(!e||!e.isReactComponent)}function wf(e){if(typeof e=="function")return lo(e)?1:0;if(e!=null){if(e=e.$$typeof,e===ki)return 11;if(e===bi)return 14}return 2}function Et(e,t){var n=e.alternate;return n===null?(n=_e(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function Xr(e,t,n,r,l,i){var o=2;if(r=e,typeof e=="function")lo(e)&&(o=1);else if(typeof e=="string")o=5;else e:switch(e){case nn:return Wt(n.children,l,i,t);case Si:o=8,l|=8;break;case yl:return e=_e(12,n,t,l|2),e.elementType=yl,e.lanes=i,e;case vl:return e=_e(13,n,t,l),e.elementType=vl,e.lanes=i,e;case jl:return e=_e(19,n,t,l),e.elementType=jl,e.lanes=i,e;case Qa:return Rs(n,l,i,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case Ba:o=10;break e;case Wa:o=9;break e;case ki:o=11;break e;case bi:o=14;break e;case ft:o=16,r=null;break e}throw Error(k(130,e==null?e:typeof e,""))}return t=_e(o,n,t,l),t.elementType=e,t.type=r,t.lanes=i,t}function Wt(e,t,n,r){return e=_e(7,e,r,t),e.lanes=n,e}function Rs(e,t,n,r){return e=_e(22,e,r,t),e.elementType=Qa,e.lanes=n,e.stateNode={isHidden:!1},e}function ml(e,t,n){return e=_e(6,e,null,t),e.lanes=n,e}function fl(e,t,n){return t=_e(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Nf(e,t,n,r,l){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=Ks(0),this.expirationTimes=Ks(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Ks(0),this.identifierPrefix=r,this.onRecoverableError=l,this.mutableSourceEagerHydrationData=null}function io(e,t,n,r,l,i,o,a,c){return e=new Nf(e,t,n,a,c),t===1?(t=1,i===!0&&(t|=8)):t=0,i=_e(3,null,null,t),e.current=i,i.stateNode=e,i.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Hi(i),e}function Sf(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:tn,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function Ru(e){if(!e)return Mt;e=e._reactInternals;e:{if(Zt(e)!==e||e.tag!==1)throw Error(k(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Se(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(k(171))}if(e.tag===1){var n=e.type;if(Se(n))return Rc(e,n,t)}return t}function $u(e,t,n,r,l,i,o,a,c){return e=io(n,r,!0,e,l,i,o,a,c),e.context=Ru(null),n=e.current,r=ge(),l=Ct(n),i=lt(r,l),i.callback=t??null,kt(n,i,l),e.current.lanes=l,yr(e,l,r),ke(e,r),e}function $s(e,t,n,r){var l=t.current,i=ge(),o=Ct(l);return n=Ru(n),t.context===null?t.context=n:t.pendingContext=n,t=lt(i,o),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=kt(l,t,o),e!==null&&(Be(e,l,o,i),Wr(e,l,o)),o}function Ns(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function ya(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function oo(e,t){ya(e,t),(e=e.alternate)&&ya(e,t)}function kf(){return null}var Ou=typeof reportError=="function"?reportError:function(e){console.error(e)};function ao(e){this._internalRoot=e}Os.prototype.render=ao.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(k(409));$s(e,t,null,null)};Os.prototype.unmount=ao.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;Yt(function(){$s(null,e,null,null)}),t[ot]=null}};function Os(e){this._internalRoot=e}Os.prototype.unstable_scheduleHydration=function(e){if(e){var t=gc();e={blockedOn:null,target:e,priority:t};for(var n=0;n<ht.length&&t!==0&&t<ht[n].priority;n++);ht.splice(n,0,e),n===0&&yc(e)}};function co(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function Us(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function va(){}function bf(e,t,n,r,l){if(l){if(typeof r=="function"){var i=r;r=function(){var f=Ns(o);i.call(f)}}var o=$u(t,r,e,0,null,!1,!1,"",va);return e._reactRootContainer=o,e[ot]=o.current,or(e.nodeType===8?e.parentNode:e),Yt(),o}for(;l=e.lastChild;)e.removeChild(l);if(typeof r=="function"){var a=r;r=function(){var f=Ns(c);a.call(f)}}var c=io(e,0,!1,null,null,!1,!1,"",va);return e._reactRootContainer=c,e[ot]=c.current,or(e.nodeType===8?e.parentNode:e),Yt(function(){$s(t,c,n,r)}),c}function Fs(e,t,n,r,l){var i=n._reactRootContainer;if(i){var o=i;if(typeof l=="function"){var a=l;l=function(){var c=Ns(o);a.call(c)}}$s(t,o,e,l)}else o=bf(n,t,e,l,r);return Ns(o)}pc=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=Vn(t.pendingLanes);n!==0&&(Li(t,n|1),ke(t,te()),!(U&6)&&(Cn=te()+500,_t()))}break;case 13:Yt(function(){var r=at(e,1);if(r!==null){var l=ge();Be(r,e,1,l)}}),oo(e,1)}};Ti=function(e){if(e.tag===13){var t=at(e,134217728);if(t!==null){var n=ge();Be(t,e,134217728,n)}oo(e,134217728)}};hc=function(e){if(e.tag===13){var t=Ct(e),n=at(e,t);if(n!==null){var r=ge();Be(n,e,t,r)}oo(e,t)}};gc=function(){return H};xc=function(e,t){var n=H;try{return H=e,t()}finally{H=n}};Dl=function(e,t,n){switch(t){case"input":if(Sl(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var l=Ms(r);if(!l)throw Error(k(90));Ga(r),Sl(r,l)}}}break;case"textarea":Ya(e,n);break;case"select":t=n.value,t!=null&&pn(e,!!n.multiple,t,!1)}};rc=no;sc=Yt;var Cf={usingClientEntryPoint:!1,Events:[jr,on,Ms,tc,nc,no]},On={findFiberByHostInstance:Ft,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},Ef={bundleType:On.bundleType,version:On.version,rendererPackageName:On.rendererPackageName,rendererConfig:On.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:ut.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=oc(e),e===null?null:e.stateNode},findFiberByHostInstance:On.findFiberByHostInstance||kf,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var $r=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!$r.isDisabled&&$r.supportsFiber)try{Es=$r.inject(Ef),Ye=$r}catch{}}Te.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Cf;Te.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!co(t))throw Error(k(200));return Sf(e,t,null,n)};Te.createRoot=function(e,t){if(!co(e))throw Error(k(299));var n=!1,r="",l=Ou;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(l=t.onRecoverableError)),t=io(e,1,!1,null,null,n,!1,r,l),e[ot]=t.current,or(e.nodeType===8?e.parentNode:e),new ao(t)};Te.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(k(188)):(e=Object.keys(e).join(","),Error(k(268,e)));return e=oc(t),e=e===null?null:e.stateNode,e};Te.flushSync=function(e){return Yt(e)};Te.hydrate=function(e,t,n){if(!Us(t))throw Error(k(200));return Fs(null,e,t,!0,n)};Te.hydrateRoot=function(e,t,n){if(!co(e))throw Error(k(405));var r=n!=null&&n.hydratedSources||null,l=!1,i="",o=Ou;if(n!=null&&(n.unstable_strictMode===!0&&(l=!0),n.identifierPrefix!==void 0&&(i=n.identifierPrefix),n.onRecoverableError!==void 0&&(o=n.onRecoverableError)),t=$u(t,null,e,1,n??null,l,!1,i,o),e[ot]=t.current,or(e),r)for(e=0;e<r.length;e++)n=r[e],l=n._getVersion,l=l(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,l]:t.mutableSourceEagerHydrationData.push(n,l);return new Os(t)};Te.render=function(e,t,n){if(!Us(t))throw Error(k(200));return Fs(null,e,t,!1,n)};Te.unmountComponentAtNode=function(e){if(!Us(e))throw Error(k(40));return e._reactRootContainer?(Yt(function(){Fs(null,null,e,!1,function(){e._reactRootContainer=null,e[ot]=null})}),!0):!1};Te.unstable_batchedUpdates=no;Te.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!Us(n))throw Error(k(200));if(e==null||e._reactInternals===void 0)throw Error(k(38));return Fs(e,t,n,!1,r)};Te.version="18.3.1-next-f1338f8080-20240426";function Uu(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(Uu)}catch(e){console.error(e)}}Uu(),Ua.exports=Te;var Lf=Ua.exports,ja=Lf;gl.createRoot=ja.createRoot,gl.hydrateRoot=ja.hydrateRoot;const pl="http://localhost:5000/api";class Tf{constructor(t){Me(this,"baseURL");this.baseURL=t}async request(t,n={}){const r=`${this.baseURL}${t}`,l=new AbortController,i=setTimeout(()=>l.abort(),1e4),o={headers:{"Content-Type":"application/json",...n.headers},signal:l.signal,...n};try{const a=await fetch(r,o);if(clearTimeout(i),!a.ok){const c=await a.json().catch(()=>({}));throw new Error(c.error||`HTTP ${a.status}: ${a.statusText}`)}return await a.json()}catch(a){throw clearTimeout(i),console.error(`API request failed: ${r}`,a),a}}async get(t){return this.request(t,{method:"GET"})}async post(t,n){return this.request(t,{method:"POST",body:n?JSON.stringify(n):void 0})}async put(t,n){return this.request(t,{method:"PUT",body:n?JSON.stringify(n):void 0})}async delete(t){return this.request(t,{method:"DELETE"})}}class Df{constructor(){Me(this,"client");Me(this,"isOnline",!0);Me(this,"connectionPromise",null);this.client=new Tf(pl)}async checkConnection(){if(this.connectionPromise)return console.log("[ApiService] Connection check already in progress, reusing promise"),this.connectionPromise;this.connectionPromise=this._performConnectionCheck();try{return await this.connectionPromise}finally{this.connectionPromise=null}}async _performConnectionCheck(){try{const n=`${pl.replace("/api","")}/health`;console.log(`[ApiService] Checking connection to: ${n}`);const r=new AbortController;let l=null;try{l=setTimeout(()=>{console.log("[ApiService] Connection check timeout, aborting request"),r.abort()},8e3);const i=await fetch(n,{signal:r.signal,method:"GET",cache:"no-cache",headers:{"Cache-Control":"no-cache"}});return l&&(clearTimeout(l),l=null),console.log(`[ApiService] Response status: ${i.status}`),i.ok?(this.isOnline=!0,console.log("[ApiService] Connection check successful"),!0):(this.isOnline=!1,console.warn(`[ApiService] Connection check failed with status: ${i.status}`),!1)}catch(i){throw l&&clearTimeout(l),i}}catch(t){return console.warn("[ApiService] Backend server is not available:",t),t instanceof Error&&(t.name==="AbortError"?console.warn("[ApiService] Request was aborted (likely due to timeout)"):t.name==="TypeError"&&t.message.includes("fetch")?console.warn("[ApiService] Network error or server not reachable"):console.warn(`[ApiService] Unexpected error: ${t.name} - ${t.message}`)),this.isOnline=!1,!1}}getConnectionStatus(){return this.isOnline}async getLLMConfigs(){return this.client.get("/llm-configs")}async createLLMConfig(t){return this.client.post("/llm-configs",t)}async updateLLMConfig(t,n){return this.client.put(`/llm-configs/${t}`,n)}async deleteLLMConfig(t){try{await this.client.delete(`/llm-configs/${t}`)}catch(n){throw n instanceof Error&&n.message.includes("无法删除正在使用的LLM配置")?new Error("无法删除正在使用的LLM配置，请先从智能体中移除此配置"):n}}async getAgents(){return this.client.get("/agents")}async createAgent(t){return this.client.post("/agents",t)}async updateAgent(t,n){return this.client.put(`/agents/${t}`,n)}async deleteAgent(t){await this.client.delete(`/agents/${t}`)}async getDiscussions(){return this.client.get("/discussions")}async createDiscussion(t){return this.client.post("/discussions",t)}async updateDiscussion(t,n){return this.client.put(`/discussions/${t}`,n)}async deleteDiscussion(t){await this.client.delete(`/discussions/${t}`)}async addMessage(t,n){return this.client.post(`/discussions/${t}/messages`,n)}async getSettings(){return this.client.get("/settings")}async updateSettings(t){return this.client.put("/settings",t)}async getPreferences(){return this.client.get("/preferences")}async updatePreferences(t){return this.client.put("/preferences",t)}async exportData(){return this.client.get("/data/export")}async importData(t,n=!1){await this.client.post("/data/import",{...t,clearExisting:n})}async clearAllData(){await this.client.delete("/data/clear")}async getStorageInfo(){return this.client.get("/storage/info")}}const B=new Df,Ot=class Ot{constructor(){Me(this,"isInitialized",!1);Me(this,"storageMode","server");Me(this,"serverAvailable",!1)}static getInstance(){return Ot.instance||(Ot.instance=new Ot),Ot.instance}async initialize(){if(this.isInitialized)return;console.log("Starting StorageService initialization..."),console.log("Checking server connection..."),console.log("API_BASE_URL: http://localhost:5000/api");let t=0;const n=3;for(;t<n&&!this.serverAvailable;){t++,console.log(`[StorageService] Connection attempt ${t}/${n}`);try{if(this.serverAvailable=await B.checkConnection(),console.log(`[StorageService] Connection check result: ${this.serverAvailable}`),this.serverAvailable)break;t<n&&(console.log("[StorageService] Waiting 2 seconds before retry..."),await new Promise(r=>setTimeout(r,2e3)))}catch(r){console.error(`Server connection check failed (attempt ${t}):`,r),this.serverAvailable=!1,t<n&&(console.log("[StorageService] Waiting 2 seconds before retry..."),await new Promise(l=>setTimeout(l,2e3)))}}if(console.log(`Server available: ${this.serverAvailable}`),!this.serverAvailable)throw new Error("服务器连接失败，请确保后端服务正在运行");await this.determineStorageMode(),console.log(`Storage mode: ${this.storageMode}`),await this.initializeDefaultSettings(),console.log("Default settings initialized"),await this.migrateData(),console.log("Data migration completed"),this.isInitialized=!0,console.log(`StorageService initialized successfully with ${this.storageMode} mode`)}async determineStorageMode(){this.storageMode="server"}setStorageMode(t){this.storageMode=t}getStorageMode(){return this.storageMode}isServerAvailable(){return this.serverAvailable}async refreshServerConnection(){return this.serverAvailable=await B.checkConnection(),this.serverAvailable}async initializeDefaultSettings(){const t={version:"1.0.0",lastUpdated:new Date().toISOString(),autoSave:!0,maxStoredDiscussions:100,defaultDiscussionMode:"free",theme:"light"},n={defaultAgentCount:3,preferredLLMProvider:"openai",autoStartDiscussion:!1,showAdvancedOptions:!1,notificationsEnabled:!0,exportFormat:"json"};this.getSettings()||this.saveSettings(t),this.getPreferences()||this.savePreferences(n)}async migrateData(){const t=await this.getSettings();((t==null?void 0:t.version)||"0.0.0")<"1.0.0"&&console.log("Migrating data to version 1.0.0...")}async saveAgents(t){try{if(this.serverAvailable){console.log(`[StorageService] Starting batch save for ${t.length} agents`);const n=await this.getAgents(),r=t.map(async l=>n.findIndex(o=>o.id===l.id)>=0?B.updateAgent(l.id,l):B.createAgent(l));await Promise.all(r),console.log(`[StorageService] Successfully saved ${t.length} agents to server`)}else throw new Error("服务器不可用，无法保存智能体数据");this.updateLastModified()}catch(n){throw console.error("Failed to save agents:",n),new Error("Failed to save agents to storage")}}async getAgents(){if(this.serverAvailable)return await B.getAgents();throw new Error("服务器不可用，无法获取智能体数据")}async saveAgent(t){if(this.serverAvailable)(await this.getAgents()).findIndex(l=>l.id===t.id)>=0?await B.updateAgent(t.id,t):await B.createAgent(t);else throw new Error("服务器不可用，无法保存智能体")}async deleteAgent(t){if(this.serverAvailable)await B.deleteAgent(t);else throw new Error("服务器不可用，无法删除智能体")}async saveLLMConfigs(t){console.log(`[StorageService] Starting saveLLMConfigs with ${t.length} configs`),console.log(`[StorageService] Storage mode: ${this.storageMode}, Server available: ${this.serverAvailable}`);try{if(this.serverAvailable){console.log("[StorageService] Attempting to save LLM configs to server...");const n=await this.getLLMConfigs(),r=t.map(async l=>n.findIndex(o=>o.id===l.id)>=0?B.updateLLMConfig(l.id,l):B.createLLMConfig(l));await Promise.all(r),console.log(`[StorageService] Successfully saved ${t.length} LLM configs to server`)}else throw new Error("服务器不可用，无法保存LLM配置");this.updateLastModified(),console.log("[StorageService] saveLLMConfigs completed successfully")}catch(n){throw console.error("[StorageService] Failed to save LLM configs:",n),new Error("Failed to save LLM configs to storage")}}async getLLMConfigs(){if(this.serverAvailable){console.log("[StorageService] Using SERVER mode - attempting to load from server");const t=await B.getLLMConfigs();return console.log(`[StorageService] Successfully loaded ${t.length} configs from server`),t}else throw new Error("服务器不可用，无法获取LLM配置数据")}async saveLLMConfig(t){if(console.log(`[StorageService] Starting saveLLMConfig for config: ${t.id} (${t.name})`),console.log(`[StorageService] Storage mode: ${this.storageMode}, Server available: ${this.serverAvailable}`),this.serverAvailable){console.log("[StorageService] Attempting to save single config to server...");const r=(await this.getLLMConfigs()).findIndex(l=>l.id===t.id);console.log(`[StorageService] Existing config index: ${r}`),r>=0?(await B.updateLLMConfig(t.id,t),console.log("[StorageService] Config updated on server successfully")):(await B.createLLMConfig(t),console.log("[StorageService] Config created on server successfully")),console.log("[StorageService] saveLLMConfig completed successfully")}else throw new Error("服务器不可用，无法保存LLM配置")}async deleteLLMConfig(t){if(console.log(`[StorageService] Starting deleteLLMConfig for config: ${t}`),console.log(`[StorageService] Storage mode: ${this.storageMode}, Server available: ${this.serverAvailable}`),this.serverAvailable)console.log("[StorageService] Attempting to delete config from server..."),await B.deleteLLMConfig(t),console.log("[StorageService] Config deleted from server successfully"),console.log("[StorageService] deleteLLMConfig completed successfully");else throw new Error("服务器不可用，无法删除LLM配置")}async exportLLMConfigs(){try{const n=(await this.getLLMConfigs()).map(r=>({...r,apiKey:"***HIDDEN***"}));return JSON.stringify(n,null,2)}catch(t){throw console.error("Failed to export LLM configs:",t),new Error("导出LLM配置失败")}}async importLLMConfigs(t){const n={success:0,errors:[]};try{const r=JSON.parse(t);if(!Array.isArray(r))throw new Error("数据格式错误：应该是配置数组");for(const l of r)try{if(!l.id||!l.name||!l.provider||!l.model){n.errors.push(`配置 ${l.name||"Unknown"} 缺少必要字段`);continue}if(l.apiKey==="***HIDDEN***"){n.errors.push(`配置 ${l.name} 的API密钥需要重新设置`);continue}await this.saveLLMConfig(l),n.success++}catch(i){n.errors.push(`导入配置 ${l.name||"Unknown"} 失败: ${i instanceof Error?i.message:"未知错误"}`)}}catch(r){n.errors.push("解析JSON数据失败: "+(r instanceof Error?r.message:"未知错误"))}return n}generateLLMConfigId(){return`llm_${Date.now()}_${Math.random().toString(36).substring(2,11)}`}createDefaultLLMConfig(t,n,r){return{id:this.generateLLMConfigId(),name:t.name,provider:t.provider.toLowerCase(),model:t.model,apiKey:n,baseURL:r,temperature:t.defaultSettings.temperature,maxTokens:t.defaultSettings.maxTokens}}async getLLMConfig(t){try{return(await this.getLLMConfigs()).find(r=>r.id===t)||null}catch(n){return console.error("Failed to get LLM config:",n),null}}validateLLMConfig(t){var r,l,i,o;const n=[];return(r=t.name)!=null&&r.trim()||n.push("配置名称不能为空"),t.provider||n.push("请选择提供商"),(l=t.model)!=null&&l.trim()||n.push("模型名称不能为空"),(i=t.apiKey)!=null&&i.trim()||n.push("API密钥不能为空"),t.temperature!==void 0&&(t.temperature<0||t.temperature>2)&&n.push("温度值应在0-2之间"),t.maxTokens!==void 0&&(t.maxTokens<1||t.maxTokens>4e3)&&n.push("最大令牌数应在1-4000之间"),t.provider==="azure"&&!((o=t.baseURL)!=null&&o.trim())&&n.push("Azure提供商需要设置基础URL"),n}async getLLMConfigStats(){try{const t=await this.getLLMConfigs(),n={};t.forEach(l=>{n[l.provider]=(n[l.provider]||0)+1});const r=t.slice(0,5);return{total:t.length,byProvider:n,recentlyUsed:r}}catch(t){return console.error("Failed to get LLM config stats:",t),{total:0,byProvider:{},recentlyUsed:[]}}}async saveDiscussions(t){try{const n=await this.getSettings(),r=(n==null?void 0:n.maxStoredDiscussions)||100,l=t.sort((i,o)=>new Date(o.createdAt).getTime()-new Date(i.createdAt).getTime()).slice(0,r);if(this.serverAvailable){console.log(`[StorageService] Starting batch save for ${l.length} discussions`);const i=await this.getDiscussions(),o=l.map(async a=>i.findIndex(f=>f.id===a.id)>=0?B.updateDiscussion(a.id,a):B.createDiscussion(a));await Promise.all(o),console.log(`[StorageService] Successfully saved ${l.length} discussions to server`)}else throw new Error("服务器不可用，无法保存讨论数据");this.updateLastModified()}catch(n){throw console.error("Failed to save discussions:",n),new Error("Failed to save discussions to storage")}}async getDiscussions(){if(this.serverAvailable)return await B.getDiscussions();throw new Error("服务器不可用，无法获取讨论数据")}async saveDiscussion(t){try{if(this.serverAvailable)(await this.getDiscussions()).findIndex(l=>l.id===t.id)>=0?await B.updateDiscussion(t.id,t):await B.createDiscussion(t);else throw new Error("服务器不可用，无法保存讨论")}catch(n){throw console.error("Failed to save discussion:",n),n}}async deleteDiscussion(t){try{if(this.serverAvailable)await B.deleteDiscussion(t);else throw new Error("服务器不可用，无法删除讨论")}catch(n){throw console.error("Failed to delete discussion:",n),n}}async saveSettings(t){if(this.serverAvailable)await B.updateSettings(t);else throw new Error("服务器不可用，无法保存设置")}async getSettings(){if(this.serverAvailable)return await B.getSettings();throw new Error("服务器不可用，无法获取设置数据")}async savePreferences(t){try{if(this.serverAvailable)await B.updatePreferences(t);else throw new Error("服务器不可用，无法保存用户偏好")}catch(n){throw console.error("Failed to save preferences:",n),new Error("Failed to save preferences to storage")}}async getPreferences(){try{if(this.serverAvailable)return await B.getPreferences();throw new Error("服务器不可用，无法获取用户偏好数据")}catch(t){throw console.error("Failed to load preferences:",t),t}}async getAllData(){return{agents:await this.getAgents(),llmConfigs:await this.getLLMConfigs(),discussions:await this.getDiscussions(),settings:await this.getSettings()||{},preferences:await this.getPreferences()||{}}}async importAllData(t){if(this.serverAvailable)await B.importData(t,!1);else throw new Error("服务器不可用，无法导入数据")}async clearAllData(){if(this.serverAvailable)await B.clearAllData();else throw new Error("服务器不可用，无法清除数据")}async updateLastModified(){const t=await this.getSettings();t&&(t.lastUpdated=new Date().toISOString(),await this.saveSettings(t))}validateData(t){try{return!(t.agents&&!Array.isArray(t.agents)||t.llmConfigs&&!Array.isArray(t.llmConfigs)||t.discussions&&!Array.isArray(t.discussions))}catch{return!1}}async getStorageInfo(){if(this.serverAvailable)return await B.getStorageInfo();throw new Error("服务器不可用，无法获取存储信息")}};Me(Ot,"instance");let ci=Ot;const F=ci.getInstance();let Or;const Mf=new Uint8Array(16);function Af(){if(!Or&&(Or=typeof crypto<"u"&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto),!Or))throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return Or(Mf)}const oe=[];for(let e=0;e<256;++e)oe.push((e+256).toString(16).slice(1));function If(e,t=0){return oe[e[t+0]]+oe[e[t+1]]+oe[e[t+2]]+oe[e[t+3]]+"-"+oe[e[t+4]]+oe[e[t+5]]+"-"+oe[e[t+6]]+oe[e[t+7]]+"-"+oe[e[t+8]]+oe[e[t+9]]+"-"+oe[e[t+10]]+oe[e[t+11]]+oe[e[t+12]]+oe[e[t+13]]+oe[e[t+14]]+oe[e[t+15]]}const _f=typeof crypto<"u"&&crypto.randomUUID&&crypto.randomUUID.bind(crypto),wa={randomUUID:_f};function ui(e,t,n){if(wa.randomUUID&&!t&&!e)return wa.randomUUID();e=e||{};const r=e.random||(e.rng||Af)();if(r[6]=r[6]&15|64,r[8]=r[8]&63|128,t){n=n||0;for(let l=0;l<16;++l)t[n+l]=r[l];return t}return If(r)}const Fu={agents:[],currentDiscussion:null,allDiscussions:[],isDiscussionActive:!1,settings:null,preferences:null,isLoading:!0,loadingStep:"storage"},Vu=L.createContext({state:Fu,dispatch:()=>null,addAgent:()=>null,updateAgent:()=>null,deleteAgent:()=>null,startDiscussion:()=>null,setCurrentDiscussion:()=>null,endDiscussion:()=>null,sendMessage:()=>null,updateSettings:()=>null,updatePreferences:()=>null,exportData:async()=>"",importData:async()=>!1,clearAllData:async()=>{}});function Pf(e,t){switch(t.type){case"ADD_AGENT":return{...e,agents:[...e.agents,t.payload]};case"UPDATE_AGENT":return{...e,agents:e.agents.map(o=>o.id===t.payload.id?t.payload:o)};case"DELETE_AGENT":return{...e,agents:e.agents.filter(o=>o.id!==t.payload)};case"START_DISCUSSION":const n={id:ui(),topic:t.payload.topic,mode:t.payload.mode,participants:t.payload.selectedAgents,messages:[],status:"active",consensus:null,createdAt:new Date,consensusScore:0,moderatorId:t.payload.moderatorId,moderatorSummaries:[],topicRelevanceScore:1,moderatorInterventions:0};return{...e,currentDiscussion:n,isDiscussionActive:!0};case"SET_CURRENT_DISCUSSION":return{...e,currentDiscussion:t.payload,isDiscussionActive:!1};case"ADD_MESSAGE":if(!e.currentDiscussion)return e;const r={...e.currentDiscussion,messages:[...e.currentDiscussion.messages,t.payload]};return{...e,currentDiscussion:r};case"UPDATE_CONSENSUS":if(!e.currentDiscussion)return e;const l={...e.currentDiscussion,consensusScore:t.payload.consensusScore,consensus:t.payload.consensus||e.currentDiscussion.consensus,status:t.payload.consensusScore>80?"consensus":e.currentDiscussion.status,moderatorInterventions:t.payload.moderatorInterventions??e.currentDiscussion.moderatorInterventions,topicRelevanceScore:t.payload.topicRelevanceScore??e.currentDiscussion.topicRelevanceScore,moderatorSummaries:t.payload.moderatorSummaries??e.currentDiscussion.moderatorSummaries};return{...e,currentDiscussion:l};case"END_DISCUSSION":if(!e.currentDiscussion)return e;const i={...e.currentDiscussion,status:"ended"};return{...e,currentDiscussion:null,allDiscussions:[i,...e.allDiscussions],isDiscussionActive:!1};case"LOAD_STATE":return t.payload;case"UPDATE_SETTINGS":return{...e,settings:t.payload};case"UPDATE_PREFERENCES":return{...e,preferences:t.payload};case"SET_LOADING":return{...e,isLoading:t.payload};case"SET_LOADING_STEP":return{...e,loadingStep:t.payload};case"SET_ALL_DISCUSSIONS":return{...e,allDiscussions:t.payload};case"INITIALIZE_SUCCESS":return{...e,agents:t.payload.agents,allDiscussions:t.payload.discussions,settings:t.payload.settings,preferences:t.payload.preferences,isLoading:!1,loadingStep:"complete"};default:return e}}function zf({children:e}){const[t,n]=L.useReducer(Pf,Fu);L.useEffect(()=>{let N=!1;return(async()=>{if(N)return;const d=Date.now();try{console.log("Starting app initialization..."),n({type:"SET_LOADING",payload:!0}),n({type:"SET_LOADING_STEP",payload:"storage"}),console.log("Initializing storage service...");const u=Date.now();if(await F.initialize(),N||(console.log(`Storage service initialized in ${Date.now()-u}ms`),n({type:"SET_LOADING_STEP",payload:"server"}),F.isServerAvailable()||console.warn("后端服务器不可用，系统无法正常工作"),N))return;n({type:"SET_LOADING_STEP",payload:"agents"}),console.log("Loading data...");const j=Date.now(),S=await y();n({type:"SET_LOADING_STEP",payload:"discussions"}),console.log(`Data loaded in ${Date.now()-j}ms`),n({type:"INITIALIZE_SUCCESS",payload:S}),console.log(`App initialization completed in ${Date.now()-d}ms`)}catch(u){throw console.error("Failed to initialize app:",u),console.error("Error details:",{message:u instanceof Error?u.message:"Unknown error",stack:u instanceof Error?u.stack:void 0,initTime:Date.now()-d}),u}})(),()=>{N=!0}},[]),L.useEffect(()=>{if(!t.isLoading){const N=setTimeout(async()=>{try{console.log("Auto-saving data...");const m=[];m.push(F.saveAgents(t.agents));const d=t.currentDiscussion?[...t.allDiscussions,t.currentDiscussion]:t.allDiscussions;m.push(F.saveDiscussions(d)),t.settings&&m.push(F.saveSettings(t.settings)),t.preferences&&m.push(F.savePreferences(t.preferences)),await Promise.all(m),console.log("Auto-save completed")}catch(m){console.error("Failed to auto-save data:",m)}},1e3);return()=>clearTimeout(N)}},[t.agents,t.allDiscussions,t.currentDiscussion,t.settings,t.preferences,t.isLoading]);const r=N=>{const m={...N,id:ui(),isActive:!0};n({type:"ADD_AGENT",payload:m})},l=N=>{n({type:"UPDATE_AGENT",payload:N})},i=async N=>{try{await F.deleteAgent(N),n({type:"DELETE_AGENT",payload:N})}catch(m){throw console.error("Failed to delete agent:",m),m}},o=N=>{n({type:"START_DISCUSSION",payload:N})},a=N=>{n({type:"SET_CURRENT_DISCUSSION",payload:N})},c=async N=>{if(t.currentDiscussion)try{const m={...t.currentDiscussion,status:"ended",endReason:N||"手动终止"};await F.saveDiscussion(m),n({type:"END_DISCUSSION"});const d=await F.getDiscussions();n({type:"SET_ALL_DISCUSSIONS",payload:d})}catch(m){console.error("Failed to save discussion:",m),n({type:"END_DISCUSSION"})}else n({type:"END_DISCUSSION"})},f=async(N,m,d)=>{const u={id:ui(),agentId:m,content:N,type:d,timestamp:new Date};if(n({type:"ADD_MESSAGE",payload:u}),t.currentDiscussion)try{await B.addMessage(t.currentDiscussion.id,u)}catch(p){console.error("Failed to save message:",p)}},x=N=>{n({type:"UPDATE_SETTINGS",payload:N})},g=N=>{n({type:"UPDATE_PREFERENCES",payload:N})},y=async()=>{try{const[N,m,d,u]=await Promise.all([F.getAgents(),F.getDiscussions(),F.getSettings(),F.getPreferences()]);return{agents:N||[],discussions:m||[],settings:d||{},preferences:u||{}}}catch(N){throw console.error("Failed to load data:",N),N}},v=async()=>{try{const N=await F.getAllData();return JSON.stringify(N,null,2)}catch(N){throw console.error("Failed to export data:",N),new Error("导出数据失败")}},w=async N=>{try{const m=JSON.parse(N);if(!F.validateData(m))throw new Error("数据格式无效");await F.importAllData(m);const d=await y();return n({type:"INITIALIZE_SUCCESS",payload:d}),!0}catch(m){return console.error("Failed to import data:",m),!1}},h=async()=>{try{await F.clearAllData(),n({type:"INITIALIZE_SUCCESS",payload:{agents:[],discussions:[],settings:{},preferences:{}}})}catch(N){throw console.error("Failed to clear data:",N),new Error("清除数据失败")}};return s.jsx(Vu.Provider,{value:{state:t,dispatch:n,addAgent:r,updateAgent:l,deleteAgent:i,startDiscussion:o,setCurrentDiscussion:a,endDiscussion:c,sendMessage:f,updateSettings:x,updatePreferences:g,exportData:v,importData:w,clearAllData:h},children:e})}const Pt=()=>{const e=L.useContext(Vu);if(!e)throw new Error("useApp must be used within AppProvider");return e};var Rf={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};const $f=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),Of=(e,t)=>{const n=L.forwardRef(({color:r="currentColor",size:l=24,strokeWidth:i=2,absoluteStrokeWidth:o,children:a,...c},f)=>L.createElement("svg",{ref:f,...Rf,width:l,height:l,stroke:r,strokeWidth:o?Number(i)*24/Number(l):i,className:`lucide lucide-${$f(e)}`,...c},[...t.map(([x,g])=>L.createElement(x,g)),...(Array.isArray(a)?a:[a])||[]]));return n.displayName=`${e}`,n};var P=Of;const En=P("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]),di=P("AlertTriangle",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z",key:"c3ski4"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]),hl=P("BarChart3",[["path",{d:"M3 3v18h18",key:"1s2lah"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]]),Lt=P("Brain",[["path",{d:"M9.5 2A2.5 2.5 0 0 1 12 4.5v15a2.5 2.5 0 0 1-4.96.44 2.5 2.5 0 0 1-2.96-3.08 3 3 0 0 1-.34-5.58 2.5 2.5 0 0 1 1.32-4.24 2.5 2.5 0 0 1 1.98-3A2.5 2.5 0 0 1 9.5 2Z",key:"1mhkh5"}],["path",{d:"M14.5 2A2.5 2.5 0 0 0 12 4.5v15a2.5 2.5 0 0 0 4.96.44 2.5 2.5 0 0 0 2.96-3.08 3 3 0 0 0 .34-5.58 2.5 2.5 0 0 0-1.32-4.24 2.5 2.5 0 0 0-1.98-3A2.5 2.5 0 0 0 14.5 2Z",key:"1d6s00"}]]),Uf=P("Calendar",[["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",ry:"2",key:"eu3xkr"}],["line",{x1:"16",x2:"16",y1:"2",y2:"6",key:"m3sa8f"}],["line",{x1:"8",x2:"8",y1:"2",y2:"6",key:"18kwsl"}],["line",{x1:"3",x2:"21",y1:"10",y2:"10",key:"xt86sb"}]]),vt=P("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["polyline",{points:"22 4 12 14.01 9 11.01",key:"6xbx8j"}]]),Ff=P("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]]),Vf=P("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]]),Hu=P("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]),Bu=P("Cpu",[["rect",{x:"4",y:"4",width:"16",height:"16",rx:"2",key:"1vbyd7"}],["rect",{x:"9",y:"9",width:"6",height:"6",key:"o3kz5p"}],["path",{d:"M15 2v2",key:"13l42r"}],["path",{d:"M15 20v2",key:"15mkzm"}],["path",{d:"M2 15h2",key:"1gxd5l"}],["path",{d:"M2 9h2",key:"1bbxkp"}],["path",{d:"M20 15h2",key:"19e6y8"}],["path",{d:"M20 9h2",key:"19tzq7"}],["path",{d:"M9 2v2",key:"165o2o"}],["path",{d:"M9 20v2",key:"i2bqo8"}]]),uo=P("Database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]]),mi=P("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]]),Hf=P("ExternalLink",[["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}],["polyline",{points:"15 3 21 3 21 9",key:"mznyad"}],["line",{x1:"10",x2:"21",y1:"14",y2:"3",key:"18c3s4"}]]),Bf=P("EyeOff",[["path",{d:"M9.88 9.88a3 3 0 1 0 4.24 4.24",key:"1jxqfv"}],["path",{d:"M10.73 5.08A10.43 10.43 0 0 1 12 5c7 0 10 7 10 7a13.16 13.16 0 0 1-1.67 2.68",key:"9wicm4"}],["path",{d:"M6.61 6.61A13.526 13.526 0 0 0 2 12s3 7 10 7a9.74 9.74 0 0 0 5.39-1.61",key:"1jreej"}],["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}]]),Wf=P("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),Qf=P("FileText",[["path",{d:"M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z",key:"1nnpy2"}],["polyline",{points:"14 2 14 8 20 8",key:"1ew0cm"}],["line",{x1:"16",x2:"8",y1:"13",y2:"13",key:"14keom"}],["line",{x1:"16",x2:"8",y1:"17",y2:"17",key:"17nazh"}],["line",{x1:"10",x2:"8",y1:"9",y2:"9",key:"1a5vjj"}]]),Kf=P("HardDrive",[["line",{x1:"22",x2:"2",y1:"12",y2:"12",key:"1y58io"}],["path",{d:"M5.45 5.11 2 12v6a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-6l-3.45-6.89A2 2 0 0 0 16.76 4H7.24a2 2 0 0 0-1.79 1.11z",key:"oot6mr"}],["line",{x1:"6",x2:"6.01",y1:"16",y2:"16",key:"sgf278"}],["line",{x1:"10",x2:"10.01",y1:"16",y2:"16",key:"1l4acy"}]]),Wu=P("HelpCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3",key:"1u773s"}],["path",{d:"M12 17h.01",key:"p32p05"}]]),Ss=P("History",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}],["path",{d:"M12 7v5l4 2",key:"1fdv2h"}]]),Gf=P("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]]),Na=P("Loader",[["line",{x1:"12",x2:"12",y1:"2",y2:"6",key:"gza1u7"}],["line",{x1:"12",x2:"12",y1:"18",y2:"22",key:"1qhbu9"}],["line",{x1:"4.93",x2:"7.76",y1:"4.93",y2:"7.76",key:"xae44r"}],["line",{x1:"16.24",x2:"19.07",y1:"16.24",y2:"19.07",key:"bxnmvf"}],["line",{x1:"2",x2:"6",y1:"12",y2:"12",key:"89khin"}],["line",{x1:"18",x2:"22",y1:"12",y2:"12",key:"pb8tfm"}],["line",{x1:"4.93",x2:"7.76",y1:"19.07",y2:"16.24",key:"1uxjnu"}],["line",{x1:"16.24",x2:"19.07",y1:"7.76",y2:"4.93",key:"6duxfx"}]]),Sa=P("MessageCircle",[["path",{d:"m3 21 1.9-5.7a8.5 8.5 0 1 1 3.8 3.8z",key:"v2veuj"}]]),Ve=P("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]]),Qu=P("PenSquare",[["path",{d:"M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1qinfi"}],["path",{d:"M18.5 2.5a2.12 2.12 0 0 1 3 3L12 15l-4 1 1-4Z",key:"w2jsv5"}]]),qf=P("Play",[["polygon",{points:"5 3 19 12 5 21 5 3",key:"191637"}]]),fi=P("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]),Yf=P("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]]),Xf=P("Save",[["path",{d:"M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z",key:"1owoqh"}],["polyline",{points:"17 21 17 13 7 13 7 21",key:"1md35c"}],["polyline",{points:"7 3 7 8 15 8",key:"8nz8an"}]]),Zf=P("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]]),Tt=P("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),Jf=P("StopCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["rect",{width:"6",height:"6",x:"9",y:"9",key:"1wrtvo"}]]),ep=P("TestTube",[["path",{d:"M14.5 2v17.5c0 1.4-1.1 2.5-2.5 2.5h0c-1.4 0-2.5-1.1-2.5-2.5V2",key:"187lwq"}],["path",{d:"M8.5 2h7",key:"csnxdl"}],["path",{d:"M14.5 16h-5",key:"1ox875"}]]),Ku=P("ThumbsDown",[["path",{d:"M17 14V2",key:"8ymqnk"}],["path",{d:"M9 18.12 10 14H4.17a2 2 0 0 1-1.92-2.56l2.33-8A2 2 0 0 1 6.5 2H20a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2h-2.76a2 2 0 0 0-1.79 1.11L12 22h0a3.13 3.13 0 0 1-3-3.88Z",key:"s6e0r"}]]),Gu=P("ThumbsUp",[["path",{d:"M7 10v12",key:"1qc93n"}],["path",{d:"M15 5.88 14 10h5.83a2 2 0 0 1 1.92 2.56l-2.33 8A2 2 0 0 1 17.5 22H4a2 2 0 0 1-2-2v-8a2 2 0 0 1 2-2h2.76a2 2 0 0 0 1.79-1.11L12 2h0a3.13 3.13 0 0 1 3 3.88Z",key:"y3tblf"}]]),gr=P("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]),qu=P("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]]),pi=P("Upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]]),tp=P("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]),st=P("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]]),np=P("Wrench",[["path",{d:"M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z",key:"cbrjhi"}]]),rp=P("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]),ka=P("Zap",[["polygon",{points:"13 2 3 14 12 14 11 22 21 10 12 10 13 2",key:"45s27k"}]]),sp=[{id:"gpt-4-turbo",name:"GPT-4 Turbo",provider:"OpenAI",model:"gpt-4-turbo-preview",description:"OpenAI最新的GPT-4 Turbo模型，性能强大，适合复杂推理",defaultSettings:{temperature:.7,maxTokens:1e3}},{id:"gpt-4",name:"GPT-4",provider:"OpenAI",model:"gpt-4",description:"OpenAI的旗舰模型，适合需要高质量输出的场景",defaultSettings:{temperature:.7,maxTokens:1e3}},{id:"gpt-3.5-turbo",name:"GPT-3.5 Turbo",provider:"OpenAI",model:"gpt-3.5-turbo",description:"快速且经济的模型，适合大多数对话任务",defaultSettings:{temperature:.7,maxTokens:800}},{id:"claude-3-opus",name:"Claude 3 Opus",provider:"Anthropic",model:"claude-3-opus-20240229",description:"Anthropic最强大的模型，擅长复杂分析和创作",defaultSettings:{temperature:.7,maxTokens:1e3}},{id:"claude-3-sonnet",name:"Claude 3 Sonnet",provider:"Anthropic",model:"claude-3-sonnet-20240229",description:"平衡性能和成本的优秀选择",defaultSettings:{temperature:.7,maxTokens:800}},{id:"claude-3-haiku",name:"Claude 3 Haiku",provider:"Anthropic",model:"claude-3-haiku-20240307",description:"快速响应的轻量级模型，适合简单任务",defaultSettings:{temperature:.7,maxTokens:600}},{id:"gpt-4-azure",name:"Azure GPT-4",provider:"Azure",model:"gpt-4",description:"部署在Azure上的GPT-4模型",defaultSettings:{temperature:.7,maxTokens:1e3}},{id:"gpt-35-turbo-azure",name:"Azure GPT-3.5 Turbo",provider:"Azure",model:"gpt-35-turbo",description:"部署在Azure上的GPT-3.5 Turbo模型",defaultSettings:{temperature:.7,maxTokens:800}}],ba=sp;function ks(e){return{openai:"🤖",anthropic:"🧠",azure:"☁️",custom:"⚙️"}[e.toLowerCase()]||"🔧"}function bs(e){return{openai:"bg-green-100 text-green-800",anthropic:"bg-blue-100 text-blue-800",azure:"bg-purple-100 text-purple-800",custom:"bg-gray-100 text-gray-800"}[e.toLowerCase()]||"bg-gray-100 text-gray-800"}const Ca=["/images/agent-alex.jpg","/images/agent-luna.jpg","/images/agent-max.jpg","/images/agent-chen.jpg","/images/agent-sam.jpg","/images/agent-robin.jpg","/images/agent-taylor.jpg","/images/agent-zoe.jpg"],lp=["技术","商业","设计","营销","数据分析","产品管理","法律","心理学","教育","医疗"],Yu=[{value:"logical",label:"逻辑型"},{value:"creative",label:"创意型"},{value:"analytical",label:"分析型"},{value:"intuitive",label:"直觉型"},{value:"systematic",label:"系统型"}],ip=[{value:"assertive",label:"果断型"},{value:"collaborative",label:"协作型"},{value:"diplomatic",label:"外交型"},{value:"direct",label:"直接型"},{value:"thoughtful",label:"深思型"}],op=["数据查询","市场分析","技术调研","用户调研","竞品分析","风险评估","法律咨询","创意生成"];function ap(){const{state:e,addAgent:t,updateAgent:n,deleteAgent:r}=Pt(),[l,i]=L.useState(!1),[o,a]=L.useState(null),c=async x=>{if(confirm("确定要删除这个智能体吗？"))try{await r(x)}catch(g){alert("删除智能体失败: "+(g instanceof Error?g.message:"未知错误"))}},f=x=>{o?(n({...x,id:o.id,isActive:o.isActive}),a(null)):(t(x),i(!1))};return s.jsx("div",{className:"h-full bg-gradient-to-br from-slate-50 to-blue-50 w-full overflow-y-auto",children:s.jsx("div",{className:"centered-container",children:s.jsxs("div",{className:"centered-content",children:[s.jsxs("div",{className:"flex items-center justify-between mb-8",children:[s.jsxs("div",{children:[s.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"智能体管理"}),s.jsx("p",{className:"text-gray-600",children:"配置和管理您的AI智能体团队"})]}),s.jsxs("button",{onClick:()=>i(!0),className:"flex items-center gap-2 bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors shadow-lg",children:[s.jsx(fi,{size:20}),"添加智能体"]})]}),s.jsx("div",{className:"flex flex-wrap gap-6 mb-8",children:e.agents.map(x=>s.jsx(cp,{agent:x,onEdit:()=>a(x),onDelete:()=>c(x.id)},x.id))}),(l||o)&&s.jsx(up,{agent:o,onSubmit:f,onCancel:()=>{i(!1),a(null)}})]})})})}function cp({agent:e,onEdit:t,onDelete:n}){var r,l,i;return s.jsxs("div",{className:"bg-white rounded-xl shadow-lg hover:shadow-xl transition-shadow p-6 border border-gray-100 fixed-size-card",children:[s.jsxs("div",{className:"flex items-center gap-4 mb-4",children:[s.jsx("img",{src:e.avatar,alt:e.name,className:"w-16 h-16 rounded-full object-cover border-4 border-blue-100"}),s.jsxs("div",{children:[s.jsx("h3",{className:"text-xl font-semibold text-gray-900",children:e.name}),s.jsx("span",{className:`inline-block px-2 py-1 rounded-full text-xs font-medium ${e.isActive?"bg-green-100 text-green-800":"bg-gray-100 text-gray-600"}`,children:e.isActive?"活跃":"非活跃"})]})]}),s.jsxs("div",{className:"space-y-3 mb-4",children:[s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx(tp,{size:16,className:"text-blue-600"}),s.jsx("span",{className:"text-sm text-gray-600",children:"专业领域："}),s.jsxs("div",{className:"flex flex-wrap gap-1",children:[e.expertise.slice(0,2).map((o,a)=>s.jsx("span",{className:"bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded",children:o},a)),e.expertise.length>2&&s.jsxs("span",{className:"text-xs text-gray-500",children:["+",e.expertise.length-2]})]})]}),s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx(Lt,{size:16,className:"text-purple-600"}),s.jsx("span",{className:"text-sm text-gray-600",children:"思维方式："}),s.jsx("span",{className:"text-sm font-medium text-gray-900",children:(r=Yu.find(o=>o.value===e.thinkingStyle))==null?void 0:r.label})]}),s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx(np,{size:16,className:"text-green-600"}),s.jsx("span",{className:"text-sm text-gray-600",children:"工具："}),s.jsxs("span",{className:"text-sm text-gray-500",children:[e.tools.length," 个"]})]}),e.llmConfig&&s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx(Tt,{size:16,className:"text-orange-600"}),s.jsx("span",{className:"text-sm text-gray-600",children:"LLM："}),s.jsxs("div",{className:"flex items-center gap-1",children:[s.jsx("span",{className:"text-sm",children:ks(e.llmConfig.provider)}),s.jsx("span",{className:`text-xs px-2 py-0.5 rounded ${bs(e.llmConfig.provider)}`,children:e.llmConfig.name})]})]}),e.isModerator&&s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx("div",{className:"w-4 h-4 bg-purple-600 rounded-full flex items-center justify-center",children:s.jsx("span",{className:"text-white text-xs font-bold",children:"主"})}),s.jsx("span",{className:"text-sm text-gray-600",children:"可做主持人"}),s.jsx("span",{className:"text-xs bg-purple-100 text-purple-800 px-2 py-0.5 rounded",children:((l=e.moderatorConfig)==null?void 0:l.managementStyle)==="strict"?"严格型":((i=e.moderatorConfig)==null?void 0:i.managementStyle)==="flexible"?"灵活型":"协作型"})]})]}),s.jsxs("div",{className:"flex gap-2",children:[s.jsxs("button",{onClick:t,className:"flex-1 bg-blue-50 text-blue-600 py-2 rounded-lg hover:bg-blue-100 transition-colors flex items-center justify-center gap-1",children:[s.jsx(Qu,{size:16}),"编辑"]}),s.jsxs("button",{onClick:n,className:"flex-1 bg-red-50 text-red-600 py-2 rounded-lg hover:bg-red-100 transition-colors flex items-center justify-center gap-1",children:[s.jsx(gr,{size:16}),"删除"]})]})]})}function up({agent:e,onSubmit:t,onCancel:n}){var y;const[r,l]=L.useState({name:(e==null?void 0:e.name)||"",avatar:(e==null?void 0:e.avatar)||Ca[0],expertise:(e==null?void 0:e.expertise)||[],thinkingStyle:(e==null?void 0:e.thinkingStyle)||"logical",personality:(e==null?void 0:e.personality)||"collaborative",tools:(e==null?void 0:e.tools)||[],llmConfig:(e==null?void 0:e.llmConfig)||null,isModerator:(e==null?void 0:e.isModerator)||!1,moderatorConfig:(e==null?void 0:e.moderatorConfig)||{summaryFrequency:5,interventionThreshold:.6,managementStyle:"flexible",autoTerminate:!0,maxInterventions:5}}),[i,o]=L.useState([]),[a,c]=L.useState(!0);L.useEffect(()=>{(async()=>{try{c(!0);const w=await F.getLLMConfigs();o(w)}catch(w){console.error("Failed to load LLM configs:",w),o([])}finally{c(!1)}})()},[]);const f=v=>{v.preventDefault(),r.name&&r.expertise.length>0&&r.llmConfig&&t({...r,llmConfig:r.llmConfig,isModerator:r.isModerator,moderatorConfig:r.isModerator?r.moderatorConfig:void 0})},x=v=>{l(w=>({...w,expertise:w.expertise.includes(v)?w.expertise.filter(h=>h!==v):[...w.expertise,v]}))},g=v=>{l(w=>({...w,tools:w.tools.includes(v)?w.tools.filter(h=>h!==v):[...w.tools,v]}))};return s.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50",children:s.jsxs("div",{className:"bg-white rounded-xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto",children:[s.jsx("div",{className:"p-6 border-b border-gray-200",children:s.jsx("h2",{className:"text-2xl font-bold text-gray-900",children:e?"编辑智能体":"添加新智能体"})}),s.jsxs("form",{onSubmit:f,className:"p-6 space-y-6",children:[s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"智能体名称"}),s.jsx("input",{type:"text",value:r.name,onChange:v=>l(w=>({...w,name:v.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"输入智能体名称",required:!0})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"选择头像"}),s.jsx("div",{className:"grid grid-cols-8 gap-3",children:Ca.map((v,w)=>s.jsx("button",{type:"button",onClick:()=>l(h=>({...h,avatar:v})),className:`relative rounded-lg overflow-hidden border-4 transition-all ${r.avatar===v?"border-blue-500 ring-2 ring-blue-200":"border-gray-200 hover:border-gray-300"}`,children:s.jsx("img",{src:v,alt:`Avatar ${w+1}`,className:"w-16 h-16 object-cover"})},w))})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"专业领域（至少选择一个）"}),s.jsx("div",{className:"grid grid-cols-2 gap-2",children:lp.map(v=>s.jsx("button",{type:"button",onClick:()=>x(v),className:`px-3 py-2 rounded-lg text-sm transition-colors ${r.expertise.includes(v)?"bg-blue-100 text-blue-800 border-2 border-blue-300":"bg-gray-100 text-gray-700 border-2 border-gray-300 hover:bg-gray-200"}`,children:v},v))})]}),s.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"思维方式"}),s.jsx("select",{value:r.thinkingStyle,onChange:v=>l(w=>({...w,thinkingStyle:v.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:Yu.map(v=>s.jsx("option",{value:v.value,children:v.label},v.value))})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"性格特征"}),s.jsx("select",{value:r.personality,onChange:v=>l(w=>({...w,personality:v.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:ip.map(v=>s.jsx("option",{value:v.value,children:v.label},v.value))})]})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"可用工具"}),s.jsx("div",{className:"grid grid-cols-2 gap-2",children:op.map(v=>s.jsx("button",{type:"button",onClick:()=>g(v),className:`px-3 py-2 rounded-lg text-sm transition-colors ${r.tools.includes(v)?"bg-green-100 text-green-800 border-2 border-green-300":"bg-gray-100 text-gray-700 border-2 border-gray-300 hover:bg-gray-200"}`,children:v},v))})]}),s.jsxs("div",{children:[s.jsxs("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:["LLM配置 ",s.jsx("span",{className:"text-red-500",children:"*"})]}),a?s.jsxs("div",{className:"w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50 flex items-center gap-2",children:[s.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"}),s.jsx("span",{className:"text-gray-600",children:"正在加载LLM配置..."})]}):s.jsxs("select",{value:((y=r.llmConfig)==null?void 0:y.id)||"",onChange:v=>{const w=i.find(h=>h.id===v.target.value);l(h=>({...h,llmConfig:w||null}))},className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",required:!0,children:[s.jsx("option",{value:"",children:"请选择LLM配置"}),i.map(v=>s.jsxs("option",{value:v.id,children:[ks(v.provider)," ",v.name," (",v.provider.toUpperCase(),")"]},v.id))]}),r.llmConfig&&s.jsxs("div",{className:"mt-2 p-3 bg-gray-50 rounded-lg",children:[s.jsxs("div",{className:"flex items-center gap-2 mb-1",children:[s.jsx("span",{className:"text-sm font-medium text-gray-700",children:"已选择:"}),s.jsx("span",{className:`text-xs px-2 py-1 rounded ${bs(r.llmConfig.provider)}`,children:r.llmConfig.name})]}),s.jsxs("div",{className:"text-xs text-gray-600",children:["模型: ",r.llmConfig.model," | 温度: ",r.llmConfig.temperature," | 令牌: ",r.llmConfig.maxTokens]})]}),i.length===0&&s.jsx("div",{className:"mt-2 p-3 bg-yellow-50 border border-yellow-200 rounded-lg",children:s.jsxs("p",{className:"text-sm text-yellow-800",children:["暂无可用的LLM配置。请先前往",s.jsx("span",{className:"font-medium",children:"LLM配置"}),"页面创建LLM配置，然后再创建智能体。"]})})]}),s.jsxs("div",{className:"border-t border-gray-200 pt-6",children:[s.jsxs("div",{className:"flex items-center gap-3 mb-4",children:[s.jsx("input",{type:"checkbox",id:"isModerator",checked:r.isModerator,onChange:v=>l(w=>({...w,isModerator:v.target.checked})),className:"w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"}),s.jsx("label",{htmlFor:"isModerator",className:"text-sm font-medium text-gray-700",children:"可以做主持人"})]}),r.isModerator&&s.jsxs("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4 space-y-4",children:[s.jsx("h4",{className:"font-medium text-blue-900 mb-3",children:"主持人配置"}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"总结频率（每N条消息）"}),s.jsx("input",{type:"number",min:"1",max:"20",value:r.moderatorConfig.summaryFrequency,onChange:v=>l(w=>({...w,moderatorConfig:{...w.moderatorConfig,summaryFrequency:parseInt(v.target.value)||5}})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"干预阈值（0-1）"}),s.jsx("input",{type:"number",min:"0",max:"1",step:"0.1",value:r.moderatorConfig.interventionThreshold,onChange:v=>l(w=>({...w,moderatorConfig:{...w.moderatorConfig,interventionThreshold:parseFloat(v.target.value)||.6}})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"管理风格"}),s.jsxs("select",{value:r.moderatorConfig.managementStyle,onChange:v=>l(w=>({...w,moderatorConfig:{...w.moderatorConfig,managementStyle:v.target.value}})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[s.jsx("option",{value:"strict",children:"严格型"}),s.jsx("option",{value:"flexible",children:"灵活型"}),s.jsx("option",{value:"collaborative",children:"协作型"})]})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"最大干预次数"}),s.jsx("input",{type:"number",min:"1",max:"20",value:r.moderatorConfig.maxInterventions,onChange:v=>l(w=>({...w,moderatorConfig:{...w.moderatorConfig,maxInterventions:parseInt(v.target.value)||5}})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]})]}),s.jsxs("div",{className:"flex items-center gap-3",children:[s.jsx("input",{type:"checkbox",id:"autoTerminate",checked:r.moderatorConfig.autoTerminate,onChange:v=>l(w=>({...w,moderatorConfig:{...w.moderatorConfig,autoTerminate:v.target.checked}})),className:"w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"}),s.jsx("label",{htmlFor:"autoTerminate",className:"text-sm font-medium text-gray-700",children:"自动终止讨论"})]})]})]}),s.jsxs("div",{className:"flex gap-3 pt-4 border-t border-gray-200",children:[s.jsx("button",{type:"button",onClick:n,className:"flex-1 px-4 py-3 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors",children:"取消"}),s.jsx("button",{type:"submit",disabled:a||i.length===0,className:"flex-1 px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors",children:a?"加载中...":`${e?"更新":"创建"}智能体`})]})]})]})})}function dp(){var x,g,y,v,w;const{state:e,startDiscussion:t}=Pt(),[n,r]=L.useState({topic:"",mode:"free",selectedAgents:[],moderatorId:void 0}),[l,i]=L.useState([]),o=()=>{const h=[];return n.topic.trim()||h.push("请输入讨论话题"),n.selectedAgents.length<2&&h.push("至少需要选择2个智能体参与讨论"),n.selectedAgents.length>8&&h.push("最多支持8个智能体同时讨论"),n.mode==="moderator"&&!n.moderatorId&&(n.selectedAgents.map(m=>f.find(d=>d.id===m)).filter(m=>m&&m.isModerator).length===0?h.push("主持人模式需要至少一个具备主持人能力的智能体"):h.push("主持人模式下请选择一个主持人")),i(h),h.length===0},a=()=>{o()&&t(n)},c=h=>{r(N=>({...N,selectedAgents:N.selectedAgents.includes(h)?N.selectedAgents.filter(m=>m!==h):[...N.selectedAgents,h]}))},f=e.agents.filter(h=>h.isActive);return f.length===0?s.jsx("div",{className:"h-full bg-gradient-to-br from-orange-50 to-red-50 overflow-y-auto",children:s.jsx("div",{className:"max-w-4xl mx-auto p-6",children:s.jsxs("div",{className:"bg-white rounded-xl shadow-lg p-8 text-center",children:[s.jsx(En,{size:64,className:"text-orange-500 mx-auto mb-4"}),s.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"没有可用的智能体"}),s.jsx("p",{className:"text-gray-600 mb-6",children:"您需要先创建和配置智能体才能开始讨论。"}),s.jsx("button",{onClick:()=>window.location.reload(),className:"bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors",children:"前往智能体管理"})]})})}):s.jsx("div",{className:"h-full bg-gradient-to-br from-purple-50 to-pink-50 w-full overflow-y-auto",children:s.jsx("div",{className:"flex justify-center p-6",children:s.jsxs("div",{className:"bg-white rounded-xl shadow-lg overflow-hidden",style:{width:"800px",maxWidth:"800px"},children:[s.jsxs("div",{className:"bg-gradient-to-r from-purple-600 to-pink-600 text-white p-8",children:[s.jsxs("div",{className:"flex items-center gap-3 mb-4",children:[s.jsx(Sa,{size:32}),s.jsx("h1",{className:"text-3xl font-bold",children:"创建新讨论"})]}),s.jsx("p",{className:"text-purple-100",children:"配置讨论话题、模式和参与者，开始智能体之间的协作讨论"})]}),s.jsxs("div",{className:"p-8 space-y-8",children:[l.length>0&&s.jsxs("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:[s.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[s.jsx(En,{size:20,className:"text-red-600"}),s.jsx("h3",{className:"font-medium text-red-800",children:"配置错误"})]}),s.jsx("ul",{className:"text-red-700 text-sm space-y-1",children:l.map((h,N)=>s.jsxs("li",{children:["• ",h]},N))})]}),s.jsxs("div",{className:"space-y-3",children:[s.jsx("label",{className:"block text-lg font-semibold text-gray-900",children:"讨论话题"}),s.jsx("textarea",{value:n.topic,onChange:h=>r(N=>({...N,topic:h.target.value})),placeholder:"请输入您想要讨论的话题，例如：如何提升用户体验设计质量？",className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent resize-none",rows:3}),s.jsx("p",{className:"text-sm text-gray-500",children:"清晰的话题描述有助于智能体更好地理解和参与讨论"})]}),s.jsxs("div",{className:"space-y-4",children:[s.jsx("label",{className:"block text-lg font-semibold text-gray-900",children:"讨论模式"}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[s.jsxs("button",{onClick:()=>r(h=>({...h,mode:"free"})),className:`p-6 rounded-xl border-2 transition-all text-left ${n.mode==="free"?"border-purple-500 bg-purple-50":"border-gray-200 hover:border-gray-300"}`,children:[s.jsxs("div",{className:"flex items-center gap-3 mb-3",children:[s.jsx(Sa,{size:24,className:n.mode==="free"?"text-purple-600":"text-gray-600"}),s.jsx("h3",{className:"font-semibold text-gray-900",children:"自由讨论模式"})]}),s.jsx("p",{className:"text-gray-600 text-sm",children:"智能体根据话题相关性和兴趣自主发言，讨论更加自然流畅"})]}),s.jsxs("button",{onClick:()=>r(h=>({...h,mode:"moderator"})),className:`p-6 rounded-xl border-2 transition-all text-left ${n.mode==="moderator"?"border-purple-500 bg-purple-50":"border-gray-200 hover:border-gray-300"}`,children:[s.jsxs("div",{className:"flex items-center gap-3 mb-3",children:[s.jsx(Tt,{size:24,className:n.mode==="moderator"?"text-purple-600":"text-gray-600"}),s.jsx("h3",{className:"font-semibold text-gray-900",children:"主持人模式"})]}),s.jsx("p",{className:"text-gray-600 text-sm",children:"选择一个智能体作为主持人，按轮次组织讨论，更加有序规范"})]})]})]}),s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{className:"flex items-center gap-3",children:[s.jsx(st,{size:24,className:"text-purple-600"}),s.jsxs("h2",{className:"text-lg font-semibold text-gray-900",children:["选择参与者 (",n.selectedAgents.length,"/8)"]})]}),s.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:f.map(h=>s.jsxs("button",{onClick:()=>c(h.id),className:`p-4 rounded-xl border-2 transition-all text-left ${n.selectedAgents.includes(h.id)?"border-purple-500 bg-purple-50":"border-gray-200 hover:border-gray-300"}`,children:[s.jsxs("div",{className:"flex items-center gap-3 mb-2",children:[s.jsx("img",{src:h.avatar,alt:h.name,className:"w-10 h-10 rounded-full object-cover"}),s.jsxs("div",{children:[s.jsx("h3",{className:"font-medium text-gray-900",children:h.name}),s.jsx("p",{className:"text-sm text-gray-500",children:h.expertise.slice(0,2).join("、")})]})]}),s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsxs("span",{className:"text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded",children:[h.thinkingStyle==="logical"&&"逻辑型",h.thinkingStyle==="creative"&&"创意型",h.thinkingStyle==="analytical"&&"分析型",h.thinkingStyle==="intuitive"&&"直觉型",h.thinkingStyle==="systematic"&&"系统型"]}),h.isModerator&&s.jsx("span",{className:"text-xs bg-blue-100 text-blue-600 px-2 py-1 rounded",children:"主持人"})]}),n.selectedAgents.includes(h.id)&&s.jsx("div",{className:"w-5 h-5 bg-purple-500 rounded-full flex items-center justify-center",children:s.jsx("div",{className:"w-2 h-2 bg-white rounded-full"})})]})]},h.id))}),s.jsx("p",{className:"text-sm text-gray-500",children:"建议选择具有不同专业背景和思维方式的智能体，以获得更丰富的讨论视角"})]}),n.selectedAgents.length>0&&s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{className:"flex items-center gap-3",children:[s.jsx(Tt,{size:24,className:"text-purple-600"}),s.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"主持人设置"})]}),s.jsxs("div",{className:"space-y-3",children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"选择主持人（可选）"}),s.jsxs("select",{value:n.moderatorId||"",onChange:h=>r(N=>({...N,moderatorId:h.target.value||void 0})),className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent",children:[s.jsx("option",{value:"",children:"无主持人（系统自动管理）"}),n.selectedAgents.map(h=>f.find(N=>N.id===h)).filter(h=>h&&h.isModerator).map(h=>s.jsxs("option",{value:h.id,children:[h.name," - ",h.expertise.slice(0,2).join("、")]},h.id))]}),n.moderatorId&&s.jsxs("div",{className:"space-y-4",children:[(()=>{var N,m,d;const h=f.find(u=>u.id===n.moderatorId);return h?s.jsx("div",{className:"bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-lg p-4",children:s.jsxs("div",{className:"flex items-start gap-4",children:[s.jsx("img",{src:h.avatar,alt:h.name,className:"w-12 h-12 rounded-full object-cover"}),s.jsxs("div",{className:"flex-1",children:[s.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[s.jsx("h4",{className:"font-semibold text-gray-900",children:h.name}),s.jsx("span",{className:"text-xs bg-blue-100 text-blue-600 px-2 py-1 rounded",children:"主持人"})]}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-3 text-sm",children:[s.jsxs("div",{children:[s.jsx("span",{className:"text-gray-600",children:"专业领域："}),s.jsx("span",{className:"text-gray-900",children:h.expertise.slice(0,3).join("、")})]}),s.jsxs("div",{children:[s.jsx("span",{className:"text-gray-600",children:"思维方式："}),s.jsxs("span",{className:"text-gray-900",children:[h.thinkingStyle==="logical"&&"逻辑型",h.thinkingStyle==="creative"&&"创意型",h.thinkingStyle==="analytical"&&"分析型",h.thinkingStyle==="intuitive"&&"直觉型",h.thinkingStyle==="systematic"&&"系统型"]})]}),s.jsxs("div",{children:[s.jsx("span",{className:"text-gray-600",children:"性格特征："}),s.jsxs("span",{className:"text-gray-900",children:[h.personality==="assertive"&&"果断型",h.personality==="collaborative"&&"协作型",h.personality==="diplomatic"&&"外交型",h.personality==="direct"&&"直接型",h.personality==="thoughtful"&&"深思型"]})]}),s.jsxs("div",{children:[s.jsx("span",{className:"text-gray-600",children:"管理风格："}),s.jsxs("span",{className:"text-gray-900",children:[((N=n.moderatorConfig)==null?void 0:N.managementStyle)==="strict"&&"严格型",((m=n.moderatorConfig)==null?void 0:m.managementStyle)==="flexible"&&"灵活型",((d=n.moderatorConfig)==null?void 0:d.managementStyle)==="collaborative"&&"协作型"]})]})]})]})]})}):null})(),s.jsx("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:s.jsxs("div",{className:"flex items-start gap-3",children:[s.jsx("div",{className:"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0",children:s.jsx(Tt,{size:16,className:"text-blue-600"})}),s.jsxs("div",{className:"flex-1",children:[s.jsx("h4",{className:"font-medium text-blue-900 mb-1",children:"主持人职责"}),s.jsxs("ul",{className:"text-sm text-blue-700 space-y-1",children:[s.jsx("li",{children:"• 实时总结讨论内容"}),s.jsx("li",{children:"• 监控话题相关性"}),s.jsx("li",{children:"• 指定发言顺序（主持人模式）"}),s.jsx("li",{children:"• 引导讨论方向"})]})]})]})}),s.jsxs("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-4",children:[s.jsx("h4",{className:"font-medium text-gray-900 mb-3",children:"主持人配置"}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"总结频率"}),s.jsxs("select",{value:((x=n.moderatorConfig)==null?void 0:x.summaryFrequency)||5,onChange:h=>r(N=>{var m,d,u,p;return{...N,moderatorConfig:{...N.moderatorConfig,summaryFrequency:parseInt(h.target.value),interventionThreshold:((m=N.moderatorConfig)==null?void 0:m.interventionThreshold)||.6,managementStyle:((d=N.moderatorConfig)==null?void 0:d.managementStyle)||"flexible",autoTerminate:((u=N.moderatorConfig)==null?void 0:u.autoTerminate)||!0,maxInterventions:((p=N.moderatorConfig)==null?void 0:p.maxInterventions)||5}}}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-purple-500 focus:border-transparent",children:[s.jsx("option",{value:3,children:"每3条消息"}),s.jsx("option",{value:5,children:"每5条消息"}),s.jsx("option",{value:8,children:"每8条消息"}),s.jsx("option",{value:10,children:"每10条消息"})]})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"管理风格"}),s.jsxs("select",{value:((g=n.moderatorConfig)==null?void 0:g.managementStyle)||"flexible",onChange:h=>r(N=>{var m,d,u,p;return{...N,moderatorConfig:{...N.moderatorConfig,summaryFrequency:((m=N.moderatorConfig)==null?void 0:m.summaryFrequency)||5,interventionThreshold:((d=N.moderatorConfig)==null?void 0:d.interventionThreshold)||.6,managementStyle:h.target.value,autoTerminate:((u=N.moderatorConfig)==null?void 0:u.autoTerminate)||!0,maxInterventions:((p=N.moderatorConfig)==null?void 0:p.maxInterventions)||5}}}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-purple-500 focus:border-transparent",children:[s.jsx("option",{value:"strict",children:"严格型 - 严格控制发言顺序"}),s.jsx("option",{value:"flexible",children:"灵活型 - 适度引导讨论"}),s.jsx("option",{value:"collaborative",children:"协作型 - 鼓励自由交流"})]})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"干预阈值"}),s.jsxs("select",{value:((y=n.moderatorConfig)==null?void 0:y.interventionThreshold)||.6,onChange:h=>r(N=>{var m,d,u,p;return{...N,moderatorConfig:{...N.moderatorConfig,summaryFrequency:((m=N.moderatorConfig)==null?void 0:m.summaryFrequency)||5,interventionThreshold:parseFloat(h.target.value),managementStyle:((d=N.moderatorConfig)==null?void 0:d.managementStyle)||"flexible",autoTerminate:((u=N.moderatorConfig)==null?void 0:u.autoTerminate)||!0,maxInterventions:((p=N.moderatorConfig)==null?void 0:p.maxInterventions)||5}}}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-purple-500 focus:border-transparent",children:[s.jsx("option",{value:.8,children:"高敏感度 - 轻微偏题即干预"}),s.jsx("option",{value:.6,children:"中等敏感度 - 适度偏题时干预"}),s.jsx("option",{value:.4,children:"低敏感度 - 严重偏题才干预"})]})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"最大干预次数"}),s.jsxs("select",{value:((v=n.moderatorConfig)==null?void 0:v.maxInterventions)||5,onChange:h=>r(N=>{var m,d,u,p;return{...N,moderatorConfig:{...N.moderatorConfig,summaryFrequency:((m=N.moderatorConfig)==null?void 0:m.summaryFrequency)||5,interventionThreshold:((d=N.moderatorConfig)==null?void 0:d.interventionThreshold)||.6,managementStyle:((u=N.moderatorConfig)==null?void 0:u.managementStyle)||"flexible",autoTerminate:((p=N.moderatorConfig)==null?void 0:p.autoTerminate)||!0,maxInterventions:parseInt(h.target.value)}}}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-purple-500 focus:border-transparent",children:[s.jsx("option",{value:3,children:"3次"}),s.jsx("option",{value:5,children:"5次"}),s.jsx("option",{value:8,children:"8次"}),s.jsx("option",{value:-1,children:"不限制"})]})]})]}),s.jsx("div",{className:"mt-4",children:s.jsxs("label",{className:"flex items-center gap-2",children:[s.jsx("input",{type:"checkbox",checked:((w=n.moderatorConfig)==null?void 0:w.autoTerminate)||!0,onChange:h=>r(N=>{var m,d,u,p;return{...N,moderatorConfig:{...N.moderatorConfig,summaryFrequency:((m=N.moderatorConfig)==null?void 0:m.summaryFrequency)||5,interventionThreshold:((d=N.moderatorConfig)==null?void 0:d.interventionThreshold)||.6,managementStyle:((u=N.moderatorConfig)==null?void 0:u.managementStyle)||"flexible",autoTerminate:h.target.checked,maxInterventions:((p=N.moderatorConfig)==null?void 0:p.maxInterventions)||5}}}),className:"w-4 h-4 text-purple-600 border-gray-300 rounded focus:ring-purple-500"}),s.jsx("span",{className:"text-sm text-gray-700",children:"允许主持人自动终止讨论"})]})})]})]}),s.jsx("p",{className:"text-sm text-gray-500",children:"主持人将负责管理讨论流程，确保讨论高效有序进行"})]})]}),s.jsxs("div",{className:"border-t border-gray-200 pt-6",children:[s.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"高级设置（可选）"}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"最大消息数量"}),s.jsx("input",{type:"number",min:"10",max:"100",value:n.maxMessages||"",onChange:h=>r(N=>({...N,maxMessages:h.target.value?parseInt(h.target.value):void 0})),placeholder:"不限制",className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"时间限制（分钟）"}),s.jsx("input",{type:"number",min:"5",max:"120",value:n.timeLimit||"",onChange:h=>r(N=>({...N,timeLimit:h.target.value?parseInt(h.target.value):void 0})),placeholder:"不限制",className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"})]})]})]}),s.jsx("div",{className:"flex justify-center pt-6",children:s.jsxs("button",{onClick:a,disabled:!n.topic.trim()||n.selectedAgents.length<2,className:"flex items-center gap-3 bg-gradient-to-r from-purple-600 to-pink-600 text-white px-8 py-4 rounded-xl hover:from-purple-700 hover:to-pink-700 disabled:from-gray-400 disabled:to-gray-500 disabled:cursor-not-allowed transition-all shadow-lg text-lg font-medium",children:[s.jsx(qf,{size:24}),"开始讨论"]})})]})]})})})}const Ut=class Ut{static getInstance(){return Ut.instance||(Ut.instance=new Ut),Ut.instance}async callLLM(t,n){try{return await this.makeAPICall(t,n)}catch(r){throw console.error("LLM API调用失败:",r),new Error(`LLM调用失败: ${r instanceof Error?r.message:"未知错误"}`)}}async generateResponse(t,n){const r={messages:[{role:"user",content:n}],temperature:t.temperature||.7,maxTokens:t.maxTokens||500,model:t.model};return(await this.callLLM(t,r)).content}async generateAgentMessage(t,n,r,l=[],i=!1){const o=this.buildSystemPrompt(t,r,i),a=this.buildConversationHistory(l,t.id),c={messages:[{role:"system",content:o},...a,{role:"user",content:`请基于当前讨论情况，就"${r}"这个话题发表你的观点。`}],temperature:t.llmConfig.temperature||.7,maxTokens:t.llmConfig.maxTokens||500,model:t.llmConfig.model};return(await this.callLLM(t.llmConfig,c)).content}buildSystemPrompt(t,n,r=!1){const l=r?`你是一个名为"${t.name}"的智能体，正在作为主持人主持关于"${n}"的讨论。`:`你是一个名为"${t.name}"的智能体，正在参与关于"${n}"的讨论。`,i=r?`作为主持人，你的职责包括：
1. 引导讨论方向，确保讨论围绕主题进行
2. 平衡各方观点，促进建设性对话
3. 适时总结要点，推进讨论进展
4. 维持讨论秩序，化解可能的冲突
5. 在适当时机推动达成共识`:`请根据你的专业背景和性格特征参与讨论，保持角色一致性。你的回复应该：
1. 体现你的专业知识和思维方式
2. 符合你的性格特征
3. 简洁明了，通常在100字以内
4. 针对讨论话题提供有价值的新观点`;return`${l}

你的特征：
- 专业领域：${t.expertise.join("、")}
- 思维方式：${t.thinkingStyle}
- 性格特征：${t.personality}
- 可用工具：${t.tools.join("、")}

${i}

${t.llmConfig.systemPrompt||""}`}buildConversationHistory(t,n){return t.slice(-10).map(l=>({role:l.agentId===n?"assistant":"user",content:`${l.agentId===n?"我":"其他参与者"}：${l.content}`}))}async makeAPICall(t,n){const r=this.getAPIUrl(t),l=this.getAPIHeaders(t),i=this.formatRequestBody(t,n),o=await fetch(r,{method:"POST",headers:l,body:JSON.stringify(i)});if(!o.ok)throw new Error(`API请求失败: ${o.status} ${o.statusText}`);const a=await o.json();return this.parseResponse(t,a)}getAPIUrl(t){if(t.baseURL)return`${t.baseURL}/chat/completions`;switch(t.provider){case"openai":return"https://api.openai.com/v1/chat/completions";case"anthropic":return"https://api.anthropic.com/v1/messages";case"azure":return`${t.baseURL}/openai/deployments/${t.model}/chat/completions?api-version=2023-12-01-preview`;default:throw new Error(`不支持的提供商: ${t.provider}`)}}getAPIHeaders(t){const n={"Content-Type":"application/json"};switch(t.provider){case"openai":case"azure":case"custom":n.Authorization=`Bearer ${t.apiKey}`;break;case"anthropic":n["x-api-key"]=t.apiKey,n["anthropic-version"]="2023-06-01";break}return n}formatRequestBody(t,n){var r;switch(t.provider){case"anthropic":return{model:n.model||t.model,max_tokens:n.maxTokens||1e3,temperature:n.temperature||.7,messages:n.messages.filter(l=>l.role!=="system"),system:((r=n.messages.find(l=>l.role==="system"))==null?void 0:r.content)||""};default:return{model:n.model||t.model,messages:n.messages,temperature:n.temperature||.7,max_tokens:n.maxTokens||1e3}}}parseResponse(t,n){var r,l,i;switch(t.provider){case"anthropic":return{content:((r=n.content[0])==null?void 0:r.text)||"",usage:n.usage?{promptTokens:n.usage.input_tokens,completionTokens:n.usage.output_tokens,totalTokens:n.usage.input_tokens+n.usage.output_tokens}:void 0,model:n.model};default:return{content:((i=(l=n.choices[0])==null?void 0:l.message)==null?void 0:i.content)||"",usage:n.usage?{promptTokens:n.usage.prompt_tokens,completionTokens:n.usage.completion_tokens,totalTokens:n.usage.total_tokens}:void 0,model:n.model}}}async testLLMConfig(t){try{const n={messages:[{role:"user",content:'请回复"测试成功"'}],temperature:.1,maxTokens:10};return await this.callLLM(t,n),!0}catch(n){return console.error("LLM配置测试失败:",n),!1}}};Me(Ut,"instance");let hi=Ut;const Jt=hi.getInstance();async function mp(e,t,n,r=[],l=!1){if(!e.llmConfig)throw new Error(`智能体 ${e.name} 未配置LLM，无法生成消息`);try{return await Jt.generateAgentMessage(e,t,n,r,l)}catch(i){throw console.error(`智能体 ${e.name} 的LLM调用失败:`,i),new Error(`智能体 ${e.name} 的LLM调用失败: ${i instanceof Error?i.message:"未知错误"}`)}}function fp(e,t){if(e.length<3)return 0;const n=e.slice(-10),r=n.filter(g=>g.type==="agreement").length/n.length,l=n.filter(g=>g.type==="disagreement").length/n.length,i=n.filter(g=>g.type==="question").length/n.length,o=new Map;n.forEach(g=>{o.set(g.agentId,(o.get(g.agentId)||0)+1)});const a=o.size,c=t.filter(g=>g.isActive).length,f=a/c;let x=0;return x+=r*40,x+=(1-l)*30,x+=i>.1&&i<.3?15:0,x+=f*15,Math.min(100,Math.max(0,x))}function pp(e,t){const r=e.slice(-15).filter(i=>i.type==="statement"||i.type==="agreement");if(r.length===0)return`关于"${t}"，参与者需要更多时间来达成共识。`;const l=hp(r.map(i=>i.content));return`经过充分讨论，大家就"${t}"达成了共识：${l.slice(0,3).join("、")}是关键要素，需要重点关注和实施。`}function hp(e){return["技术创新","用户体验","市场需求","成本控制","时间规划","质量保证","团队合作","数据分析"].filter(()=>Math.random()>.6).slice(0,5)}function Zr(e,t,n){if(e.length===0)return null;if(n==="moderator"){const r=t.slice(-e.length).map(i=>i.agentId),l=e.filter(i=>!r.includes(i));return l.length>0?l[0]:e[0]}else{const r=t.slice(-10),l=new Map;e.forEach(c=>l.set(c,0)),r.forEach(c=>{e.includes(c.agentId)&&l.set(c.agentId,(l.get(c.agentId)||0)+1)});const o=[...l.entries()].sort(([,c],[,f])=>c-f).slice(0,Math.ceil(e.length/2));return o[Math.floor(Math.random()*o.length)][0]}}async function gp(e,t,n){if(!e.llmConfig)throw new Error(`主持人 ${e.name} 未配置LLM，无法生成总结`);try{const r=`作为讨论主持人，请对以下最近的讨论内容进行简洁总结：

讨论主题：${t.topic}
最近消息：
${n.map(l=>`- ${l.content}`).join(`
`)}

请提供一个简洁的总结，突出关键观点和进展：`;return await Jt.generateResponse(e.llmConfig,r)}catch(r){return console.error("主持人总结生成失败:",r),`总结生成失败：${r instanceof Error?r.message:"未知错误"}`}}async function xp(e,t,n){if(!e.llmConfig)return .8;try{const r=`作为讨论主持人，请评估以下讨论内容与主题的相关性：

讨论主题：${t.topic}
最近消息：
${n.map(o=>`- ${o.content}`).join(`
`)}

请给出相关性评分（0-1之间的数字，1表示完全相关，0表示完全无关）。
只需要返回数字，不需要解释：`,l=await Jt.generateResponse(e.llmConfig,r),i=parseFloat(l.trim());return isNaN(i)?.8:Math.max(0,Math.min(1,i))}catch(r){return console.error("话题相关性计算失败:",r),.8}}async function yp(e,t,n,r){if(!e.llmConfig||n.length===0)return Zr(n.map(l=>l.id),r,t.mode);try{const l=n.map(f=>`${f.name}（专业：${f.expertise.join("、")}，思维：${f.thinkingStyle}）`).join(`
`),i=`作为讨论主持人，请根据以下信息选择下一个最适合发言的参与者：

讨论主题：${t.topic}
参与者信息：
${l}

最近讨论内容：
${r.slice(-5).map(f=>{const x=n.find(g=>g.id===f.agentId);return`${(x==null?void 0:x.name)||"未知"}: ${f.content}`}).join(`
`)}

请选择最适合继续这个话题的参与者，只需要返回参与者的名字：`,a=(await Jt.generateResponse(e.llmConfig,i)).trim(),c=n.find(f=>f.name===a);return(c==null?void 0:c.id)||Zr(n.map(f=>f.id),r,t.mode)}catch(l){return console.error("智能发言人选择失败:",l),Zr(n.map(i=>i.id),r,t.mode)}}async function vp(e,t,n){if(!e.llmConfig)return{off_topic:"让我们回到主题上来，继续讨论相关内容。",low_activity:"大家可以分享更多想法，让讨论更加活跃。",conflict_resolution:"我们来总结一下不同的观点，寻找共同点。",summary_request:"让我总结一下到目前为止的讨论要点。"}[n];try{const r={off_topic:`作为讨论主持人，发现讨论偏离了主题"${t.topic}"。请生成一段引导语，礼貌地将讨论拉回正轨。`,low_activity:"作为讨论主持人，发现讨论活跃度较低。请生成一段鼓励性的引导语，激发参与者的讨论热情。",conflict_resolution:"作为讨论主持人，发现参与者之间存在分歧。请生成一段中性的引导语，帮助化解冲突并推进讨论。",summary_request:"作为讨论主持人，需要对当前讨论进行阶段性总结。请生成一段总结性的引导语。"};return(await Jt.generateResponse(e.llmConfig,r[n])).trim()}catch(r){return console.error("主持人引导语生成失败:",r),"让我们继续讨论，保持专注和建设性。"}}async function jp(e,t,n){if(t.consensusScore>85)return{shouldTerminate:!0,reason:"达成共识"};if(t.moderatorInterventions>=n.maxInterventions&&n.maxInterventions>0)return{shouldTerminate:!0,reason:"主持人终止"};if(!n.autoTerminate||!e.llmConfig)return{shouldTerminate:!1};try{const r=t.messages.slice(-10),l=`作为讨论主持人，请判断以下讨论是否应该结束：

讨论主题：${t.topic}
当前共识度：${t.consensusScore}%
话题相关性：${t.topicRelevanceScore}
干预次数：${t.moderatorInterventions}

最近讨论内容：
${r.map(a=>`- ${a.content}`).join(`
`)}

请判断是否应该结束讨论，只需要回答"是"或"否"：`,o=(await Jt.generateResponse(e.llmConfig,l)).trim().toLowerCase().includes("是");return{shouldTerminate:o,reason:o?"主持人终止":void 0}}catch(r){return console.error("讨论终止判断失败:",r),{shouldTerminate:!1}}}function wp(){var dt,Mn;const{state:e,dispatch:t,endDiscussion:n,sendMessage:r,setCurrentDiscussion:l}=Pt(),[i,o]=L.useState(!1),[a,c]=L.useState(null),[f,x]=L.useState(!1),[g,y]=L.useState({totalMessages:0,consensusScore:0,activeTime:0}),[v,w]=L.useState(""),[h,N]=L.useState(1),[m,d]=L.useState(0),u=L.useRef(null),p=L.useRef(null),j=L.useRef(null),{currentDiscussion:S}=e;L.useEffect(()=>(S&&S.status==="active"&&e.isDiscussionActive?(o(!0),T()):(o(!1),j.current&&(clearTimeout(j.current),j.current=null)),()=>{p.current&&clearInterval(p.current),j.current&&clearTimeout(j.current)}),[S,e.isDiscussionActive]),L.useEffect(()=>{var M;(M=u.current)==null||M.scrollIntoView({behavior:"smooth"})},[S==null?void 0:S.messages]),L.useEffect(()=>{if(S){const M=fp(S.messages,e.agents);let I;if(e.isDiscussionActive)I=Math.floor((Date.now()-new Date(S.createdAt).getTime())/1e3);else{const z=S.messages[S.messages.length-1];z?I=Math.floor((new Date(z.timestamp).getTime()-new Date(S.createdAt).getTime())/1e3):I=0}if(y({totalMessages:S.messages.length,consensusScore:M,activeTime:I}),e.isDiscussionActive&&(t({type:"UPDATE_CONSENSUS",payload:{consensusScore:M}}),M>80&&S.status==="active")){const z=pp(S.messages,S.topic);t({type:"UPDATE_CONSENSUS",payload:{consensusScore:M,consensus:z}}),o(!1),j.current&&(clearTimeout(j.current),j.current=null),setTimeout(async()=>{await V("达成共识")},2e3)}}},[S==null?void 0:S.messages]),L.useEffect(()=>{if(!e.isDiscussionActive&&e.currentDiscussion===null&&a===null){if(e.allDiscussions.length>0){const M=e.allDiscussions[0];M.status==="ended"&&c(M)}}else e.isDiscussionActive&&e.currentDiscussion&&(c(null),x(!1))},[e.isDiscussionActive,e.currentDiscussion,e.allDiscussions,a]);const T=()=>{if(!S||S.status!=="active")return;const M=S.participants,I=e.agents.filter(C=>M.includes(C.id)),z=S.moderatorId?e.agents.find(C=>C.id===S.moderatorId):null,b=async()=>{var R,zt;if(!e.currentDiscussion||e.currentDiscussion.status!=="active"||!e.isDiscussionActive){o(!1);return}const C=e.currentDiscussion;if(z&&z.moderatorConfig){const ee=z.moderatorConfig;if(C.messages.length-m>=ee.summaryFrequency)try{const Q=await gp(z,C,C.messages.slice(-ee.summaryFrequency));w(Q),d(C.messages.length)}catch(Q){console.error("主持人总结生成失败:",Q)}if(C.messages.length>0)try{const Q=await xp(z,C,C.messages.slice(-3));if(N(Q),Q<ee.interventionThreshold){const Je=(C.moderatorInterventions||0)+1;if(Je>=ee.maxInterventions&&ee.maxInterventions>0){console.log("达到最大干预次数，主持人终止讨论"),await V("偏离话题");return}const Xu=await vp(z,C,"off_topic");if(r(Xu,z.id,"statement"),t({type:"UPDATE_CONSENSUS",payload:{consensusScore:C.consensusScore,moderatorInterventions:Je}}),e.isDiscussionActive&&((R=e.currentDiscussion)==null?void 0:R.status)==="active"){const Zu=Math.random()*3e3+2e3;j.current=setTimeout(b,Zu)}return}}catch(Q){console.error("话题相关性检查失败:",Q)}try{const Q=await jp(z,C,ee);if(Q.shouldTerminate){console.log("主持人决定终止讨论:",Q.reason),await V(Q.reason||"主持人终止");return}}catch(Q){console.error("讨论终止检查失败:",Q)}}let O;if(z&&C.mode==="moderator"?O=await yp(z,C,I,C.messages):O=Zr(M,C.messages,C.mode),O){const ee=I.find(Qe=>Qe.id===O);if(ee)try{const Qe=!!(z&&ee.id===z.id),Q=await mp(ee,C,C.topic,C.messages.slice(-5),Qe),Je=A(Q);r(Q,ee.id,Je)}catch(Qe){console.error("生成消息失败:",Qe)}}if(e.isDiscussionActive&&((zt=e.currentDiscussion)==null?void 0:zt.status)==="active"){const ee=Math.random()*3e3+2e3;j.current=setTimeout(b,ee)}else o(!1)},D=Math.random()*2e3+1e3;j.current=setTimeout(b,D)},A=M=>M.includes("我赞同")||M.includes("我同意")||M.includes("这个想法很好")?"agreement":M.includes("但是")||M.includes("我认为")||M.includes("不同的看法")?"disagreement":M.includes("？")||M.includes("我们")||M.includes("如何")?"question":"statement",V=async M=>{S&&(c({...S,status:"ended",endReason:M,finalModeratorSummary:v||""}),n(M))},_=async()=>{o(!1),j.current&&(clearTimeout(j.current),j.current=null),p.current&&(clearInterval(p.current),p.current=null),await V("手动终止")},ve=M=>{const I=Math.floor(M/60),z=M%60;return`${I}:${z.toString().padStart(2,"0")}`};if(!S){if(a){const M=e.agents.filter(I=>a.participants.includes(I.id));return s.jsx("div",{className:"h-full bg-gradient-to-br from-green-50 to-blue-50 w-full overflow-y-auto",children:s.jsxs("div",{className:"max-w-7xl mx-auto p-6 space-y-6",children:[s.jsxs("div",{className:"bg-white rounded-xl shadow-lg p-8 text-center",children:[s.jsx(vt,{size:64,className:"text-green-500 mx-auto mb-4"}),s.jsx("h2",{className:"text-3xl font-bold text-gray-900 mb-4",children:"讨论已结束"}),s.jsxs("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6",children:[s.jsx("h3",{className:"text-lg font-semibold text-blue-800 mb-2",children:"讨论话题"}),s.jsx("p",{className:"text-2xl font-bold text-blue-900",children:a.topic})]}),a.endReason&&s.jsxs("div",{className:"bg-orange-50 border border-orange-200 rounded-lg p-4 mb-6",children:[s.jsx("h4",{className:"text-sm font-semibold text-orange-800 mb-1",children:"结束原因"}),s.jsx("p",{className:"text-lg text-orange-900",children:a.endReason})]}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8",children:[s.jsxs("div",{className:"bg-blue-50 rounded-lg p-4",children:[s.jsx("div",{className:"text-2xl font-bold text-blue-600",children:a.messages.length}),s.jsx("div",{className:"text-sm text-blue-800",children:"总消息数"})]}),s.jsxs("div",{className:"bg-green-50 rounded-lg p-4",children:[s.jsxs("div",{className:"text-2xl font-bold text-green-600",children:[a.consensusScore,"%"]}),s.jsx("div",{className:"text-sm text-green-800",children:"共识度"})]}),s.jsxs("div",{className:"bg-purple-50 rounded-lg p-4",children:[s.jsx("div",{className:"text-2xl font-bold text-purple-600",children:a.participants.length}),s.jsx("div",{className:"text-sm text-purple-800",children:"参与者"})]}),s.jsxs("div",{className:"bg-orange-50 rounded-lg p-4",children:[s.jsx("div",{className:"text-2xl font-bold text-orange-600",children:Math.floor((new Date().getTime()-new Date(a.createdAt).getTime())/6e4)}),s.jsx("div",{className:"text-sm text-orange-800",children:"讨论时长(分钟)"})]})]})]}),a.consensus&&s.jsxs("div",{className:"bg-white rounded-xl shadow-lg p-8",children:[s.jsxs("div",{className:"flex items-center gap-3 mb-6",children:[s.jsx(vt,{size:32,className:"text-green-600"}),s.jsx("h3",{className:"text-2xl font-bold text-gray-900",children:"最终结论"})]}),s.jsx("div",{className:"bg-green-50 border border-green-200 rounded-lg p-6",children:s.jsx("p",{className:"text-gray-800 text-lg leading-relaxed",children:a.consensus})})]}),a.moderatorId&&s.jsxs("div",{className:"bg-white rounded-xl shadow-lg p-8",children:[s.jsxs("div",{className:"flex items-center gap-3 mb-6",children:[s.jsx(st,{size:32,className:"text-purple-600"}),s.jsx("h3",{className:"text-2xl font-bold text-gray-900",children:"主持人最后一次总结"}),s.jsx("span",{className:"bg-purple-100 text-purple-800 px-3 py-1 rounded-full text-sm font-medium",children:(()=>{const I=e.agents.find(z=>z.id===a.moderatorId);return I?I.name:"主持人"})()})]}),s.jsxs("div",{className:"bg-purple-50 border border-purple-200 rounded-lg p-6",children:[s.jsxs("div",{className:"flex items-center gap-2 mb-3",children:[s.jsx("div",{className:"w-2 h-2 bg-purple-500 rounded-full"}),s.jsx("span",{className:"text-sm text-purple-700 font-medium",children:"讨论过程中的最后一次总结"})]}),a.finalModeratorSummary?s.jsx("p",{className:"text-gray-800 text-lg leading-relaxed whitespace-pre-wrap",children:a.finalModeratorSummary}):s.jsxs("div",{className:"text-center py-4",children:[s.jsx("p",{className:"text-gray-500 text-base",children:"主持人在讨论过程中未生成总结"}),s.jsx("p",{className:"text-gray-400 text-sm mt-2",children:"可能是因为讨论时间较短或消息数量未达到总结阈值"})]})]})]}),s.jsxs("div",{className:"bg-white rounded-xl shadow-lg p-8",children:[s.jsxs("div",{className:"flex items-center gap-3 mb-6",children:[s.jsx(hl,{size:32,className:"text-indigo-600"}),s.jsx("h3",{className:"text-2xl font-bold text-gray-900",children:"消息类型分析"})]}),(()=>{const I={statement:{count:0,label:"陈述",color:"gray",icon:Ve},question:{count:0,label:"提问",color:"blue",icon:Wu},agreement:{count:0,label:"赞同",color:"green",icon:Gu},disagreement:{count:0,label:"反对",color:"red",icon:Ku}};a.messages.forEach(b=>{I[b.type]&&I[b.type].count++});const z=a.messages.length;return s.jsx("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:Object.entries(I).map(([b,D])=>{const C=z>0?Math.round(D.count/z*100):0,O=D.icon;return s.jsxs("div",{className:`bg-${D.color}-50 rounded-lg p-4 text-center`,children:[s.jsx(O,{size:24,className:`text-${D.color}-600 mx-auto mb-2`}),s.jsx("div",{className:`text-2xl font-bold text-${D.color}-600`,children:D.count}),s.jsx("div",{className:`text-sm text-${D.color}-800 font-medium`,children:D.label}),s.jsxs("div",{className:`text-xs text-${D.color}-600 mt-1`,children:[C,"%"]})]},b)})})})()]}),s.jsxs("div",{className:"bg-white rounded-xl shadow-lg p-8",children:[s.jsxs("div",{className:"flex items-center gap-3 mb-6",children:[s.jsx(st,{size:32,className:"text-blue-600"}),s.jsx("h3",{className:"text-2xl font-bold text-gray-900",children:"参与者表现"})]}),s.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:M.map(I=>{const z=a.messages.filter(R=>R.agentId===I.id).length,b=a.messages.filter(R=>R.agentId===I.id&&R.type==="agreement").length,D=a.messages.filter(R=>R.agentId===I.id&&R.type==="disagreement").length,C=a.messages.filter(R=>R.agentId===I.id&&R.type==="question").length,O=a.messages.filter(R=>R.agentId===I.id&&R.type==="statement").length;return s.jsxs("div",{className:"bg-gray-50 rounded-lg p-4",children:[s.jsxs("div",{className:"flex items-center gap-3 mb-3",children:[s.jsx("img",{src:I.avatar,alt:I.name,className:"w-12 h-12 rounded-full object-cover"}),s.jsxs("div",{children:[s.jsx("h4",{className:"font-semibold text-gray-900",children:I.name}),s.jsx("p",{className:"text-sm text-gray-500",children:I.expertise.slice(0,2).join("、")})]})]}),s.jsxs("div",{className:"space-y-2 text-sm",children:[s.jsxs("div",{className:"flex justify-between",children:[s.jsx("span",{className:"text-gray-600",children:"总发言"}),s.jsx("span",{className:"font-medium",children:z})]}),s.jsxs("div",{className:"flex justify-between",children:[s.jsx("span",{className:"text-gray-600",children:"陈述"}),s.jsx("span",{className:"font-medium text-gray-600",children:O})]}),s.jsxs("div",{className:"flex justify-between",children:[s.jsx("span",{className:"text-gray-600",children:"提问"}),s.jsx("span",{className:"font-medium text-blue-600",children:C})]}),s.jsxs("div",{className:"flex justify-between",children:[s.jsx("span",{className:"text-gray-600",children:"赞同"}),s.jsx("span",{className:"font-medium text-green-600",children:b})]}),s.jsxs("div",{className:"flex justify-between",children:[s.jsx("span",{className:"text-gray-600",children:"反对"}),s.jsx("span",{className:"font-medium text-red-600",children:D})]})]})]},I.id)})})]}),s.jsxs("div",{className:"bg-white rounded-xl shadow-lg p-8",children:[s.jsxs("div",{className:"flex items-center justify-between mb-6",children:[s.jsxs("div",{className:"flex items-center gap-3",children:[s.jsx(Ve,{size:32,className:"text-purple-600"}),s.jsx("h3",{className:"text-2xl font-bold text-gray-900",children:"完整消息记录"}),s.jsxs("span",{className:"bg-purple-100 text-purple-800 px-3 py-1 rounded-full text-sm font-medium",children:[a.messages.length," 条消息"]})]}),s.jsxs("button",{onClick:()=>x(!f),className:"flex items-center gap-2 px-4 py-2 bg-purple-100 text-purple-700 rounded-lg hover:bg-purple-200 transition-colors",children:[f?"收起消息":"展开消息",s.jsx(hl,{size:16})]})]}),f?s.jsx("div",{className:"max-h-96 overflow-y-auto space-y-4 border border-gray-200 rounded-lg p-4",children:a.messages.map(I=>{const z=M.find(b=>b.id===I.agentId);return s.jsx(Ea,{message:I,agent:z},I.id)})}):s.jsxs("div",{className:"border border-gray-200 rounded-lg p-6 text-center",children:[s.jsx("p",{className:"text-gray-500 mb-4",children:'点击"展开消息"查看完整的讨论记录'}),s.jsxs("div",{className:"flex justify-center gap-4 text-sm text-gray-400",children:[s.jsxs("span",{children:["首条消息：",new Date((dt=a.messages[0])==null?void 0:dt.timestamp).toLocaleString()]}),s.jsx("span",{children:"•"}),s.jsxs("span",{children:["末条消息：",new Date((Mn=a.messages[a.messages.length-1])==null?void 0:Mn.timestamp).toLocaleString()]})]})]})]}),s.jsxs("div",{className:"bg-white rounded-xl shadow-lg p-8",children:[s.jsxs("div",{className:"flex gap-4 justify-center",children:[s.jsx("button",{onClick:()=>{l(a),c(null),x(!1)},className:"px-8 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium",children:"回到讨论室"}),s.jsx("button",{onClick:()=>{},className:"px-8 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors font-medium",children:"查看历史"}),s.jsx("button",{onClick:()=>{const I={topic:a.topic,participants:M.map(C=>C.name),messages:a.messages.map(C=>{var O;return{speaker:((O=M.find(R=>R.id===C.agentId))==null?void 0:O.name)||"Unknown",content:C.content,type:C.type,timestamp:new Date(C.timestamp).toLocaleString()}}),consensus:a.consensus,consensusScore:a.consensusScore,duration:Math.floor((new Date().getTime()-new Date(a.createdAt).getTime())/6e4)},z=new Blob([JSON.stringify(I,null,2)],{type:"application/json"}),b=URL.createObjectURL(z),D=document.createElement("a");D.href=b,D.download=`讨论记录_${a.topic}_${new Date().toISOString().split("T")[0]}.json`,document.body.appendChild(D),D.click(),document.body.removeChild(D),URL.revokeObjectURL(b)},className:"px-8 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors font-medium",children:"导出记录"})]}),s.jsx("div",{className:"mt-6 text-center",children:s.jsx("p",{className:"text-sm text-gray-500",children:'您可以通过导航栏的"创建讨论"按钮开始新的讨论'})})]})]})})}return s.jsx("div",{className:"h-full bg-gradient-to-br from-gray-50 to-blue-50 w-full overflow-y-auto",children:s.jsx("div",{className:"discussion-container",children:s.jsxs("div",{className:"bg-white rounded-xl shadow-lg p-8 text-center max-w-2xl",children:[s.jsx(Ve,{size:64,className:"text-gray-400 mx-auto mb-4"}),s.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"没有进行中的讨论"}),s.jsx("p",{className:"text-gray-600",children:"请先创建一个新的讨论来开始智能体对话。"})]})})})}const Ze=e.agents.filter(M=>S.participants.includes(M.id));return s.jsx("div",{className:"h-screen bg-gradient-to-br from-blue-50 to-indigo-50 w-full overflow-hidden pt-16",children:s.jsx("div",{className:"discussion-container h-full",children:s.jsxs("div",{className:"flex gap-6 h-full max-w-none w-full",children:[S.moderatorId&&(()=>{const M=e.agents.find(I=>I.id===S.moderatorId);return M?s.jsx("div",{className:"fixed-size-sidebar flex-shrink-0",children:s.jsxs("div",{className:"bg-white rounded-xl shadow-lg p-6 h-fit",children:[s.jsxs("div",{className:"flex items-center gap-3 mb-4",children:[s.jsx("div",{className:"w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center",children:s.jsx(st,{size:16,className:"text-purple-600"})}),s.jsx("h3",{className:"font-semibold text-gray-900",children:"主持人"})]}),s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{className:"flex items-center gap-3",children:[s.jsx("img",{src:M.avatar,alt:M.name,className:"w-12 h-12 rounded-full object-cover"}),s.jsxs("div",{children:[s.jsx("h4",{className:"font-medium text-gray-900",children:M.name}),s.jsx("p",{className:"text-sm text-gray-500",children:M.expertise.slice(0,2).join("、")})]})]}),M.moderatorConfig&&s.jsxs("div",{className:"bg-purple-50 border border-purple-200 rounded-lg p-3",children:[s.jsx("h5",{className:"text-sm font-medium text-purple-900 mb-2",children:"管理风格"}),s.jsx("p",{className:"text-sm text-purple-700",children:M.moderatorConfig.managementStyle==="strict"?"严格型":M.moderatorConfig.managementStyle==="flexible"?"灵活型":"协作型"})]}),s.jsxs("div",{className:"space-y-2",children:[s.jsxs("div",{className:"flex items-center justify-between text-sm",children:[s.jsx("span",{className:"text-gray-600",children:"话题相关性"}),s.jsxs("span",{className:`font-medium ${h>.8?"text-green-600":h>.6?"text-yellow-600":"text-red-600"}`,children:[Math.round(h*100),"%"]})]}),s.jsx("div",{className:"w-full h-2 bg-gray-200 rounded-full overflow-hidden",children:s.jsx("div",{className:`h-full transition-all duration-500 ${h>.8?"bg-green-500":h>.6?"bg-yellow-500":"bg-red-500"}`,style:{width:`${h*100}%`}})})]}),v&&s.jsxs("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-3",children:[s.jsx("h5",{className:"text-sm font-medium text-blue-900 mb-2",children:"最新总结"}),s.jsx("p",{className:"text-sm text-blue-700",children:v})]}),s.jsxs("div",{className:"flex items-center justify-between text-sm",children:[s.jsx("span",{className:"text-gray-600",children:"干预次数"}),s.jsx("span",{className:"font-medium text-gray-900",children:S.moderatorInterventions||0})]})]})]})}):null})(),s.jsx("div",{className:"fixed-size-discussion flex-shrink-0 h-[70vh]",children:s.jsxs("div",{className:"bg-white rounded-xl shadow-lg overflow-hidden h-full flex flex-col",children:[s.jsxs("div",{className:`text-white p-6 ${e.isDiscussionActive?"bg-gradient-to-r from-blue-600 to-indigo-600":"bg-gradient-to-r from-gray-600 to-gray-700"}`,children:[s.jsxs("div",{className:"flex items-center justify-between mb-4",children:[s.jsxs("div",{className:"flex items-center gap-3",children:[s.jsx(Ve,{size:32}),s.jsxs("div",{children:[s.jsx("h1",{className:"text-2xl font-bold",children:e.isDiscussionActive?"讨论进行中":"历史讨论记录"}),s.jsx("p",{className:e.isDiscussionActive?"text-blue-100":"text-gray-100",children:S.mode==="free"?"自由讨论模式":"主持人模式"})]})]}),s.jsx("div",{className:"flex items-center gap-4",children:e.isDiscussionActive?s.jsxs(s.Fragment,{children:[s.jsxs("div",{className:"text-center",children:[s.jsx("div",{className:"text-2xl font-bold",children:ve(g.activeTime)}),s.jsx("div",{className:"text-sm text-blue-100",children:"讨论时长"})]}),s.jsxs("button",{onClick:_,className:"flex items-center gap-2 bg-red-500 hover:bg-red-600 px-4 py-2 rounded-lg transition-colors",children:[s.jsx(Jf,{size:20}),"结束讨论"]})]}):s.jsxs("div",{className:"text-center",children:[s.jsx("div",{className:"text-sm text-gray-100",children:"创建时间"}),s.jsx("div",{className:"text-lg font-bold",children:new Date(S.createdAt).toLocaleString()})]})})]}),s.jsxs("div",{className:"bg-white bg-opacity-20 rounded-lg p-4",children:[s.jsx("h3",{className:"font-semibold mb-2",children:"讨论话题"}),s.jsx("p",{className:e.isDiscussionActive?"text-blue-50":"text-gray-50",children:S.topic})]})]}),s.jsxs("div",{className:"h-[calc(100%-180px)] overflow-y-auto p-6 space-y-4",children:[S.messages.map(M=>s.jsx(Ea,{message:M,agent:Ze.find(I=>I.id===M.agentId)},M.id)),i&&e.isDiscussionActive&&s.jsxs("div",{className:"flex items-center gap-3 text-gray-500",children:[s.jsx("div",{className:"animate-spin w-5 h-5 border-2 border-blue-500 border-t-transparent rounded-full"}),s.jsx("span",{children:"智能体正在思考中..."})]}),s.jsx("div",{ref:u})]})]})}),s.jsxs("div",{className:"space-y-6 fixed-size-sidebar",children:[s.jsxs("div",{className:"bg-white rounded-xl shadow-lg p-6",children:[s.jsxs("div",{className:"flex items-center gap-3 mb-4",children:[s.jsx(qu,{size:24,className:"text-green-600"}),s.jsx("h3",{className:"font-semibold text-gray-900",children:"共识度"})]}),s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{className:"relative",children:[s.jsx("div",{className:"w-full h-4 bg-gray-200 rounded-full overflow-hidden",children:s.jsx("div",{className:`h-full transition-all duration-500 ${g.consensusScore>80?"bg-green-500":g.consensusScore>60?"bg-yellow-500":"bg-red-500"}`,style:{width:`${g.consensusScore}%`}})}),s.jsxs("div",{className:"text-center mt-2 font-bold text-2xl",children:[Math.round(g.consensusScore),"%"]})]}),S.status==="consensus"&&s.jsxs("div",{className:"flex items-center gap-2 bg-green-50 text-green-800 p-3 rounded-lg",children:[s.jsx(vt,{size:20}),s.jsx("span",{className:"font-medium",children:"已达成共识！"})]})]})]}),s.jsxs("div",{className:"bg-white rounded-xl shadow-lg p-6",children:[s.jsxs("div",{className:"flex items-center gap-3 mb-4",children:[s.jsx(st,{size:24,className:"text-blue-600"}),s.jsx("h3",{className:"font-semibold text-gray-900",children:"参与者"})]}),s.jsx("div",{className:"space-y-3",children:Ze.filter(M=>M.id!==S.moderatorId).map(M=>{const I=S.messages.filter(b=>b.agentId===M.id).length,z=S.messages.slice().reverse().find(b=>b.agentId===M.id);return s.jsxs("div",{className:"flex items-center gap-3 p-3 bg-gray-50 rounded-lg",children:[s.jsx("img",{src:M.avatar,alt:M.name,className:"w-10 h-10 rounded-full object-cover"}),s.jsxs("div",{className:"flex-1",children:[s.jsx("div",{className:"font-medium text-gray-900",children:M.name}),s.jsxs("div",{className:"text-sm text-gray-500",children:[I," 条消息"]})]}),z&&s.jsx("div",{className:"text-xs text-gray-400",children:new Date(z.timestamp).toLocaleTimeString()})]},M.id)})})]}),s.jsxs("div",{className:"bg-white rounded-xl shadow-lg p-6",children:[s.jsxs("div",{className:"flex items-center gap-3 mb-4",children:[s.jsx(hl,{size:24,className:"text-purple-600"}),s.jsx("h3",{className:"font-semibold text-gray-900",children:"讨论统计"})]}),s.jsxs("div",{className:"space-y-3",children:[s.jsxs("div",{className:"flex justify-between items-center",children:[s.jsx("span",{className:"text-gray-600",children:"总消息数"}),s.jsx("span",{className:"font-bold text-lg",children:g.totalMessages})]}),s.jsxs("div",{className:"flex justify-between items-center",children:[s.jsx("span",{className:"text-gray-600",children:"讨论时长"}),s.jsx("span",{className:"font-bold text-lg",children:ve(g.activeTime)})]}),s.jsxs("div",{className:"flex justify-between items-center",children:[s.jsx("span",{className:"text-gray-600",children:"参与者数量"}),s.jsx("span",{className:"font-bold text-lg",children:Ze.length})]})]})]}),S.consensus&&s.jsxs("div",{className:"bg-green-50 border border-green-200 rounded-xl p-6",children:[s.jsxs("div",{className:"flex items-center gap-3 mb-3",children:[s.jsx(vt,{size:24,className:"text-green-600"}),s.jsx("h3",{className:"font-semibold text-green-900",children:"讨论结论"})]}),s.jsx("p",{className:"text-green-800",children:S.consensus})]})]})]})})})}function Ea({message:e,agent:t}){const n=()=>{switch(e.type){case"question":return s.jsx(Wu,{size:16,className:"text-blue-500"});case"agreement":return s.jsx(Gu,{size:16,className:"text-green-500"});case"disagreement":return s.jsx(Ku,{size:16,className:"text-red-500"});default:return s.jsx(Ve,{size:16,className:"text-gray-500"})}},r=()=>{switch(e.type){case"question":return"border-l-blue-500";case"agreement":return"border-l-green-500";case"disagreement":return"border-l-red-500";default:return"border-l-gray-300"}};return s.jsxs("div",{className:`flex gap-4 p-4 bg-gray-50 rounded-lg border-l-4 ${r()}`,children:[s.jsx("img",{src:t==null?void 0:t.avatar,alt:t==null?void 0:t.name,className:"w-12 h-12 rounded-full object-cover flex-shrink-0"}),s.jsxs("div",{className:"flex-1",children:[s.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[s.jsx("span",{className:"font-semibold text-gray-900",children:t==null?void 0:t.name}),n(),s.jsx("span",{className:"text-xs text-gray-500",children:new Date(e.timestamp).toLocaleTimeString()})]}),s.jsx("p",{className:"text-gray-800 leading-relaxed",children:e.content})]})]})}const Np=({isOpen:e,onClose:t,onSave:n,editingConfig:r})=>{const[l,i]=L.useState({name:"",provider:"openai",model:"",apiKey:"",baseURL:"",temperature:.7,maxTokens:1e3,systemPrompt:""}),[o,a]=L.useState(!1),[c,f]=L.useState(!1),[x,g]=L.useState(null),[y,v]=L.useState([]),[w,h]=L.useState("");L.useEffect(()=>{r?(i(r),h("")):(i({name:"",provider:"openai",model:"",apiKey:"",baseURL:"",temperature:.7,maxTokens:1e3,systemPrompt:""}),h("")),g(null),v([])},[r,e]);const N=p=>{if(h(p),p){const j=ba.find(S=>S.id===p);j&&i(S=>({...S,name:j.name,provider:j.provider.toLowerCase(),model:j.model,temperature:j.defaultSettings.temperature,maxTokens:j.defaultSettings.maxTokens}))}},m=(p,j)=>{if(i(S=>({...S,[p]:j})),g(null),y.length>0){const S=F.validateLLMConfig({...l,[p]:j});v(S)}},d=async()=>{const p=F.validateLLMConfig(l);if(p.length>0){v(p);return}f(!0),g(null);try{const j={id:"test",name:l.name,provider:l.provider,model:l.model,apiKey:l.apiKey,baseURL:l.baseURL,temperature:l.temperature,maxTokens:l.maxTokens,systemPrompt:l.systemPrompt},S=await Jt.testLLMConfig(j);g({success:S,message:S?"连接测试成功！":"连接测试失败，请检查配置。"})}catch(j){g({success:!1,message:`连接测试失败: ${j instanceof Error?j.message:"未知错误"}`})}finally{f(!1)}},u=()=>{const p=F.validateLLMConfig(l);if(p.length>0){v(p);return}const j={id:(r==null?void 0:r.id)||F.generateLLMConfigId(),name:l.name,provider:l.provider,model:l.model,apiKey:l.apiKey,baseURL:l.baseURL,temperature:l.temperature,maxTokens:l.maxTokens,systemPrompt:l.systemPrompt};n(j),t()};return e?s.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:s.jsxs("div",{className:"bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto",children:[s.jsxs("div",{className:"flex justify-between items-center mb-6",children:[s.jsx("h2",{className:"text-xl font-bold",children:r?"编辑LLM配置":"新建LLM配置"}),s.jsx("button",{onClick:t,className:"text-gray-500 hover:text-gray-700",children:s.jsx(rp,{className:"w-6 h-6"})})]}),!r&&s.jsxs("div",{className:"mb-6",children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"选择预设配置（可选）"}),s.jsxs("select",{value:w,onChange:p=>N(p.target.value),className:"w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[s.jsx("option",{value:"",children:"自定义配置"}),ba.map(p=>s.jsxs("option",{value:p.id,children:[p.name," - ",p.description]},p.id))]})]}),y.length>0&&s.jsxs("div",{className:"mb-4 p-3 bg-red-50 border border-red-200 rounded-md",children:[s.jsxs("div",{className:"flex items-center",children:[s.jsx(En,{className:"w-5 h-5 text-red-500 mr-2"}),s.jsx("span",{className:"text-red-700 font-medium",children:"配置错误"})]}),s.jsx("ul",{className:"mt-2 text-sm text-red-600",children:y.map((p,j)=>s.jsxs("li",{children:["• ",p]},j))})]}),x&&s.jsx("div",{className:`mb-4 p-3 border rounded-md ${x.success?"bg-green-50 border-green-200":"bg-red-50 border-red-200"}`,children:s.jsxs("div",{className:"flex items-center",children:[x.success?s.jsx(vt,{className:"w-5 h-5 text-green-500 mr-2"}):s.jsx(En,{className:"w-5 h-5 text-red-500 mr-2"}),s.jsx("span",{className:`font-medium ${x.success?"text-green-700":"text-red-700"}`,children:x.message})]})}),s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"配置名称 *"}),s.jsx("input",{type:"text",value:l.name||"",onChange:p=>m("name",p.target.value),className:"w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"例如：我的GPT-4配置"})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"提供商 *"}),s.jsxs("select",{value:l.provider||"openai",onChange:p=>m("provider",p.target.value),className:"w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[s.jsx("option",{value:"openai",children:"OpenAI"}),s.jsx("option",{value:"anthropic",children:"Anthropic"}),s.jsx("option",{value:"azure",children:"Azure OpenAI"}),s.jsx("option",{value:"custom",children:"自定义"})]})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"模型名称 *"}),s.jsx("input",{type:"text",value:l.model||"",onChange:p=>m("model",p.target.value),className:"w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"例如：gpt-4, claude-3-opus-20240229"})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"API密钥 *"}),s.jsxs("div",{className:"relative",children:[s.jsx("input",{type:o?"text":"password",value:l.apiKey||"",onChange:p=>m("apiKey",p.target.value),className:"w-full p-2 pr-10 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"输入API密钥"}),s.jsx("button",{type:"button",onClick:()=>a(!o),className:"absolute right-2 top-2 text-gray-500 hover:text-gray-700",children:o?s.jsx(Bf,{className:"w-5 h-5"}):s.jsx(Wf,{className:"w-5 h-5"})})]})]}),(l.provider==="azure"||l.provider==="custom")&&s.jsxs("div",{children:[s.jsxs("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:["基础URL ",l.provider==="azure"?"*":"(可选)"]}),s.jsx("input",{type:"text",value:l.baseURL||"",onChange:p=>m("baseURL",p.target.value),className:"w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:l.provider==="azure"?"https://your-resource.openai.azure.com":"https://api.example.com"})]}),s.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"温度 (0-2)"}),s.jsx("input",{type:"number",min:"0",max:"2",step:"0.1",value:l.temperature||.7,onChange:p=>m("temperature",parseFloat(p.target.value)),className:"w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"最大令牌数"}),s.jsx("input",{type:"number",min:"1",max:"4000",value:l.maxTokens||1e3,onChange:p=>m("maxTokens",parseInt(p.target.value)),className:"w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"自定义系统提示词 (可选)"}),s.jsx("textarea",{value:l.systemPrompt||"",onChange:p=>m("systemPrompt",p.target.value),rows:3,className:"w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"添加额外的系统提示词..."})]})]}),s.jsxs("div",{className:"flex justify-between mt-6",children:[s.jsxs("button",{onClick:d,disabled:c,className:"flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed",children:[s.jsx(ep,{className:"w-4 h-4 mr-2"}),c?"测试中...":"测试连接"]}),s.jsxs("div",{className:"flex space-x-3",children:[s.jsx("button",{onClick:t,className:"px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50",children:"取消"}),s.jsxs("button",{onClick:u,className:"flex items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700",children:[s.jsx(Xf,{className:"w-4 h-4 mr-2"}),"保存"]})]})]})]})}):null},Sp=()=>{const[e,t]=L.useState([]),[n,r]=L.useState(!1),[l,i]=L.useState(null),[o,a]=L.useState(!1),[c,f]=L.useState(!0),[x,g]=L.useState({total:0,byProvider:{},recentlyUsed:[]});L.useEffect(()=>{y()},[]);const y=async()=>{try{f(!0);const[u,p]=await Promise.all([F.getLLMConfigs(),F.getLLMConfigStats()]);t(u),g(p)}catch(u){console.error("Failed to load configs:",u),t([]),g({total:0,byProvider:{},recentlyUsed:[]})}finally{f(!1)}},v=async u=>{try{await F.saveLLMConfig(u),await y()}catch{alert("保存配置失败")}},w=u=>{i(u),r(!0)},h=async u=>{if(confirm("确定要删除这个LLM配置吗？"))try{await F.deleteLLMConfig(u),await y()}catch{alert("删除配置失败")}},N=()=>{i(null),r(!0)},m=async()=>{try{const u=await F.exportLLMConfigs(),p=new Blob([u],{type:"application/json"}),j=URL.createObjectURL(p),S=document.createElement("a");S.href=j,S.download=`llm-configs-${new Date().toISOString().split("T")[0]}.json`,document.body.appendChild(S),S.click(),document.body.removeChild(S),URL.revokeObjectURL(j)}catch{alert("导出失败")}},d=u=>{var S;const p=(S=u.target.files)==null?void 0:S[0];if(!p)return;const j=new FileReader;j.onload=async T=>{var A;try{const V=(A=T.target)==null?void 0:A.result,_=await F.importLLMConfigs(V);_.success>0&&(alert(`成功导入 ${_.success} 个配置`),await y()),_.errors.length>0&&alert(`导入时遇到错误：
${_.errors.join(`
`)}`)}catch{alert("导入失败：文件格式错误")}},j.readAsText(p),u.target.value=""};return c?s.jsx("div",{className:"h-full overflow-y-auto",children:s.jsx("div",{className:"centered-container",children:s.jsx("div",{className:"centered-content",children:s.jsx("div",{className:"flex items-center justify-center h-64",children:s.jsxs("div",{className:"text-center",children:[s.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),s.jsx("p",{className:"text-gray-600",children:"正在加载LLM配置..."})]})})})})}):s.jsx("div",{className:"h-full overflow-y-auto",children:s.jsx("div",{className:"centered-container",children:s.jsxs("div",{className:"centered-content",children:[s.jsxs("div",{className:"flex justify-between items-center mb-6",children:[s.jsxs("div",{children:[s.jsxs("h2",{className:"text-2xl font-bold text-gray-900 flex items-center",children:[s.jsx(Lt,{className:"w-8 h-8 mr-3 text-blue-600"}),"LLM配置管理"]}),s.jsx("p",{className:"text-gray-600 mt-1",children:"管理大语言模型配置，为智能体提供AI能力"})]}),s.jsxs("div",{className:"flex space-x-3",children:[s.jsxs("button",{onClick:()=>a(!o),className:"flex items-center px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50",children:[s.jsx(Tt,{className:"w-4 h-4 mr-2"}),"导入/导出"]}),s.jsxs("button",{onClick:N,className:"flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700",children:[s.jsx(fi,{className:"w-4 h-4 mr-2"}),"新建配置"]})]})]}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-6",children:[s.jsxs("div",{className:"bg-white p-4 rounded-lg border border-gray-200",children:[s.jsx("div",{className:"text-2xl font-bold text-blue-600",children:x.total}),s.jsx("div",{className:"text-sm text-gray-600",children:"总配置数"})]}),s.jsxs("div",{className:"bg-white p-4 rounded-lg border border-gray-200",children:[s.jsx("div",{className:"text-2xl font-bold text-green-600",children:Object.keys(x.byProvider).length}),s.jsx("div",{className:"text-sm text-gray-600",children:"支持的提供商"})]}),s.jsxs("div",{className:"bg-white p-4 rounded-lg border border-gray-200",children:[s.jsx("div",{className:"text-2xl font-bold text-purple-600",children:x.recentlyUsed.length}),s.jsx("div",{className:"text-sm text-gray-600",children:"最近使用"})]})]}),o&&s.jsxs("div",{className:"bg-gray-50 p-4 rounded-lg mb-6 border border-gray-200",children:[s.jsx("h3",{className:"text-lg font-medium mb-3",children:"导入/导出配置"}),s.jsxs("div",{className:"flex space-x-4",children:[s.jsxs("button",{onClick:m,className:"flex items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700",children:[s.jsx(mi,{className:"w-4 h-4 mr-2"}),"导出配置"]}),s.jsxs("label",{className:"flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 cursor-pointer",children:[s.jsx(pi,{className:"w-4 h-4 mr-2"}),"导入配置",s.jsx("input",{type:"file",accept:".json",onChange:d,className:"hidden"})]})]}),s.jsx("p",{className:"text-sm text-gray-600 mt-2",children:"导出的配置文件会隐藏API密钥，导入时需要重新设置"})]}),s.jsx("div",{className:"bg-white rounded-lg border border-gray-200",children:e.length===0?s.jsxs("div",{className:"p-8 text-center",children:[s.jsx(Lt,{className:"w-16 h-16 text-gray-300 mx-auto mb-4"}),s.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"还没有LLM配置"}),s.jsx("p",{className:"text-gray-600 mb-4",children:"创建第一个LLM配置来为智能体提供AI能力"}),s.jsxs("button",{onClick:N,className:"flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 mx-auto",children:[s.jsx(fi,{className:"w-4 h-4 mr-2"}),"新建配置"]})]}):s.jsx("div",{className:"divide-y divide-gray-200",children:e.map(u=>s.jsxs("div",{className:"p-4 hover:bg-gray-50",children:[s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{className:"flex items-center space-x-4",children:[s.jsx("div",{className:"text-2xl",children:ks(u.provider)}),s.jsxs("div",{children:[s.jsx("h3",{className:"text-lg font-medium text-gray-900",children:u.name}),s.jsxs("div",{className:"flex items-center space-x-2 mt-1",children:[s.jsx("span",{className:`px-2 py-1 text-xs rounded-full ${bs(u.provider)}`,children:u.provider.toUpperCase()}),s.jsx("span",{className:"text-sm text-gray-600",children:u.model})]})]})]}),s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsxs("div",{className:"text-right text-sm text-gray-600",children:[s.jsxs("div",{children:["温度: ",u.temperature]}),s.jsxs("div",{children:["令牌: ",u.maxTokens]})]}),s.jsxs("div",{className:"flex space-x-1",children:[s.jsx("button",{onClick:()=>w(u),className:"p-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-md",title:"编辑配置",children:s.jsx(Qu,{className:"w-4 h-4"})}),s.jsx("button",{onClick:()=>h(u.id),className:"p-2 text-gray-600 hover:text-red-600 hover:bg-red-50 rounded-md",title:"删除配置",children:s.jsx(gr,{className:"w-4 h-4"})})]})]})]}),u.systemPrompt&&s.jsxs("div",{className:"mt-3 p-3 bg-gray-50 rounded-md",children:[s.jsx("div",{className:"text-sm text-gray-600",children:s.jsx("strong",{children:"自定义系统提示词:"})}),s.jsx("div",{className:"text-sm text-gray-800 mt-1 line-clamp-2",children:u.systemPrompt})]})]},u.id))})}),Object.keys(x.byProvider).length>0&&s.jsxs("div",{className:"mt-6 bg-white p-4 rounded-lg border border-gray-200",children:[s.jsx("h3",{className:"text-lg font-medium mb-3",children:"提供商分布"}),s.jsx("div",{className:"flex flex-wrap gap-2",children:Object.entries(x.byProvider).map(([u,p])=>s.jsxs("div",{className:`flex items-center px-3 py-1 rounded-full text-sm ${bs(u)}`,children:[s.jsx("span",{className:"mr-1",children:ks(u)}),u.toUpperCase(),": ",p]},u))})]}),s.jsx(Np,{isOpen:n,onClose:()=>{r(!1),i(null)},onSave:v,editingConfig:l})]})})})},kp=()=>{const{state:e,exportData:t,importData:n,clearAllData:r}=Pt(),[l,i]=L.useState(!1),[o,a]=L.useState(!1),[c,f]=L.useState(null),[x,g]=L.useState(!1),[y,v]=L.useState(null);L.useEffect(()=>{(async()=>{try{const u=await F.getStorageInfo();v(u)}catch(u){console.error("Failed to fetch storage info:",u),v({used:0,available:0,total:0})}})()},[]);const w=d=>{if(d===0)return"0 Bytes";const u=1024,p=["Bytes","KB","MB","GB"],j=Math.floor(Math.log(d)/Math.log(u));return parseFloat((d/Math.pow(u,j)).toFixed(2))+" "+p[j]},h=async()=>{i(!0);try{const d=await t(),u=new Blob([d],{type:"application/json"}),p=URL.createObjectURL(u),j=document.createElement("a");j.href=p,j.download=`multi-agent-system-backup-${new Date().toISOString().split("T")[0]}.json`,document.body.appendChild(j),j.click(),document.body.removeChild(j),URL.revokeObjectURL(p)}catch(d){f({success:!1,message:"导出失败: "+(d instanceof Error?d.message:"未知错误")})}finally{i(!1)}},N=async d=>{var p;const u=(p=d.target.files)==null?void 0:p[0];if(u){a(!0),f(null);try{const j=await u.text(),S=await n(j);f({success:S,message:S?"数据导入成功！":"数据导入失败，请检查文件格式。"})}catch(j){f({success:!1,message:"导入失败: "+(j instanceof Error?j.message:"未知错误")})}finally{a(!1),d.target.value=""}}},m=async()=>{try{await r(),g(!1),f({success:!0,message:"所有数据已清除，系统已重置为默认状态。"})}catch(d){f({success:!1,message:"清除数据失败: "+(d instanceof Error?d.message:"未知错误")})}};return s.jsx("div",{className:"h-full overflow-y-auto",children:s.jsx("div",{className:"centered-container",children:s.jsxs("div",{className:"centered-content",children:[s.jsx("div",{className:"flex justify-between items-center mb-6",children:s.jsxs("div",{children:[s.jsxs("h2",{className:"text-2xl font-bold text-gray-900 flex items-center",children:[s.jsx(uo,{className:"w-8 h-8 mr-3 text-blue-600"}),"数据管理"]}),s.jsx("p",{className:"text-gray-600 mt-1",children:"管理系统数据的备份、恢复和清理"})]})}),c&&s.jsx("div",{className:`mb-6 p-4 rounded-lg border ${c.success?"bg-green-50 border-green-200":"bg-red-50 border-red-200"}`,children:s.jsxs("div",{className:"flex items-center",children:[c.success?s.jsx(vt,{className:"w-5 h-5 text-green-500 mr-2"}):s.jsx(di,{className:"w-5 h-5 text-red-500 mr-2"}),s.jsx("span",{className:`font-medium ${c.success?"text-green-700":"text-red-700"}`,children:c.message})]})}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-8",children:[s.jsx("div",{className:"bg-white p-4 rounded-lg border border-gray-200",children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx("div",{className:"text-2xl font-bold text-blue-600",children:e.agents.length}),s.jsx("div",{className:"text-sm text-gray-600",children:"智能体"})]}),s.jsx(Tt,{className:"w-8 h-8 text-blue-500"})]})}),s.jsx("div",{className:"bg-white p-4 rounded-lg border border-gray-200",children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx("div",{className:"text-2xl font-bold text-green-600",children:e.allDiscussions.length}),s.jsx("div",{className:"text-sm text-gray-600",children:"历史讨论"})]}),s.jsx(Qf,{className:"w-8 h-8 text-green-500"})]})}),s.jsx("div",{className:"bg-white p-4 rounded-lg border border-gray-200",children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx("div",{className:"text-2xl font-bold text-purple-600",children:y?w(y.used):"加载中..."}),s.jsx("div",{className:"text-sm text-gray-600",children:"已用存储"})]}),s.jsx(Kf,{className:"w-8 h-8 text-purple-500"})]})})]}),s.jsxs("div",{className:"bg-white p-6 rounded-lg border border-gray-200 mb-8",children:[s.jsx("h3",{className:"text-lg font-medium mb-4",children:"存储使用情况"}),y?s.jsxs("div",{className:"space-y-3",children:[s.jsxs("div",{className:"flex justify-between text-sm",children:[s.jsx("span",{children:"已使用"}),s.jsx("span",{children:w(y.used)})]}),s.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2",children:s.jsx("div",{className:"bg-blue-500 h-2 rounded-full transition-all",style:{width:`${y.used/y.total*100}%`}})}),s.jsxs("div",{className:"flex justify-between text-sm text-gray-600",children:[s.jsxs("span",{children:["可用: ",w(y.available)]}),s.jsxs("span",{children:["总计: ",w(y.total)]})]})]}):s.jsx("div",{className:"text-center text-gray-500",children:"加载存储信息中..."})]}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[s.jsxs("div",{className:"bg-white p-6 rounded-lg border border-gray-200",children:[s.jsxs("div",{className:"flex items-center mb-4",children:[s.jsx(mi,{className:"w-6 h-6 text-green-600 mr-3"}),s.jsx("h3",{className:"text-lg font-medium",children:"导出数据"})]}),s.jsx("p",{className:"text-gray-600 mb-4 text-sm",children:"将所有配置和历史记录导出为JSON文件，用于备份或迁移。"}),s.jsxs("button",{onClick:h,disabled:l,className:"w-full flex items-center justify-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed",children:[s.jsx(mi,{className:"w-4 h-4 mr-2"}),l?"导出中...":"导出数据"]})]}),s.jsxs("div",{className:"bg-white p-6 rounded-lg border border-gray-200",children:[s.jsxs("div",{className:"flex items-center mb-4",children:[s.jsx(pi,{className:"w-6 h-6 text-blue-600 mr-3"}),s.jsx("h3",{className:"text-lg font-medium",children:"导入数据"})]}),s.jsx("p",{className:"text-gray-600 mb-4 text-sm",children:"从备份文件恢复配置和历史记录。将覆盖当前数据。"}),s.jsxs("label",{className:"w-full flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 cursor-pointer",children:[s.jsx(pi,{className:"w-4 h-4 mr-2"}),o?"导入中...":"选择文件",s.jsx("input",{type:"file",accept:".json",onChange:N,disabled:o,className:"hidden"})]})]}),s.jsxs("div",{className:"bg-white p-6 rounded-lg border border-gray-200",children:[s.jsxs("div",{className:"flex items-center mb-4",children:[s.jsx(gr,{className:"w-6 h-6 text-red-600 mr-3"}),s.jsx("h3",{className:"text-lg font-medium",children:"清除数据"})]}),s.jsx("p",{className:"text-gray-600 mb-4 text-sm",children:"清除所有数据并重置为默认状态。此操作不可撤销。"}),x?s.jsxs("div",{className:"space-y-2",children:[s.jsxs("div",{className:"flex items-center p-2 bg-red-50 border border-red-200 rounded text-sm text-red-700",children:[s.jsx(di,{className:"w-4 h-4 mr-2"}),"确定要清除所有数据吗？"]}),s.jsxs("div",{className:"flex space-x-2",children:[s.jsx("button",{onClick:m,className:"flex-1 px-3 py-2 bg-red-600 text-white rounded text-sm hover:bg-red-700",children:"确认清除"}),s.jsx("button",{onClick:()=>g(!1),className:"flex-1 px-3 py-2 bg-gray-300 text-gray-700 rounded text-sm hover:bg-gray-400",children:"取消"})]})]}):s.jsxs("button",{onClick:()=>g(!0),className:"w-full flex items-center justify-center px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700",children:[s.jsx(gr,{className:"w-4 h-4 mr-2"}),"清除所有数据"]})]})]}),s.jsx("div",{className:"mt-8 bg-blue-50 border border-blue-200 rounded-lg p-4",children:s.jsxs("div",{className:"flex items-start",children:[s.jsx(Gf,{className:"w-5 h-5 text-blue-500 mr-2 mt-0.5"}),s.jsxs("div",{className:"text-sm text-blue-700",children:[s.jsx("div",{className:"font-medium mb-1",children:"使用说明："}),s.jsxs("ul",{className:"space-y-1 text-xs",children:[s.jsx("li",{children:"• 导出的数据包含智能体配置、LLM配置、讨论历史等所有信息"}),s.jsx("li",{children:"• 导入数据会覆盖当前所有配置，建议先导出备份"}),s.jsx("li",{children:"• 清除数据会删除所有自定义配置，但会保留默认智能体"}),s.jsx("li",{children:"• 数据存储在浏览器本地，清除浏览器数据会丢失所有配置"})]})]})]})})]})})})},bp=({onNavigate:e})=>{const{state:t,dispatch:n,setCurrentDiscussion:r}=Pt(),[l,i]=L.useState(""),[o,a]=L.useState("date"),[c,f]=L.useState("all"),[x,g]=L.useState(null),[y,v]=L.useState(null),w=async u=>{if(confirm("确定要删除这条讨论记录吗？此操作不可撤销。"))try{v(u),await F.deleteDiscussion(u);const p=await F.getDiscussions();n({type:"SET_ALL_DISCUSSIONS",payload:p})}catch(p){console.error("删除讨论失败:",p),alert("删除失败，请重试")}finally{v(null)}},h=u=>{r(u),e("discussion")},N=t.allDiscussions.filter(u=>{const p=u.topic.toLowerCase().includes(l.toLowerCase()),j=c==="all"||u.status===c;return p&&j}).sort((u,p)=>{switch(o){case"date":return new Date(p.createdAt).getTime()-new Date(u.createdAt).getTime();case"topic":return u.topic.localeCompare(p.topic);case"messages":return p.messages.length-u.messages.length;default:return 0}}),m=u=>{switch(u){case"consensus":return s.jsx(vt,{className:"w-4 h-4 text-green-500"});case"ended":return s.jsx(Hu,{className:"w-4 h-4 text-gray-500"});default:return s.jsx(Ve,{className:"w-4 h-4 text-blue-500"})}},d=u=>{switch(u){case"consensus":return"已达成共识";case"ended":return"已结束";default:return"进行中"}};return s.jsx("div",{className:"h-[calc(100vh-4rem)] bg-gradient-to-br from-gray-50 to-blue-50 w-full overflow-hidden",children:s.jsx("div",{className:"h-full p-6",children:s.jsxs("div",{className:"bg-white rounded-xl shadow-lg h-full flex flex-col",children:[s.jsxs("div",{className:"bg-gradient-to-r from-purple-600 to-blue-600 text-white p-6 rounded-t-xl",children:[s.jsxs("div",{className:"flex items-center gap-3 mb-4",children:[s.jsx(Ss,{size:32}),s.jsxs("div",{children:[s.jsx("h1",{className:"text-2xl font-bold",children:"讨论历史"}),s.jsx("p",{className:"text-purple-100",children:"查看和管理历史讨论记录"})]})]}),s.jsxs("div",{className:"flex gap-4 items-center",children:[s.jsxs("div",{className:"flex-1 relative",children:[s.jsx(Zf,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"}),s.jsx("input",{type:"text",placeholder:"搜索讨论话题...",value:l,onChange:u=>i(u.target.value),className:"w-full pl-10 pr-4 py-2 rounded-lg text-gray-900 placeholder-gray-500"})]}),s.jsxs("select",{value:o,onChange:u=>a(u.target.value),className:"px-4 py-2 rounded-lg text-gray-900",children:[s.jsx("option",{value:"date",children:"按时间排序"}),s.jsx("option",{value:"topic",children:"按话题排序"}),s.jsx("option",{value:"messages",children:"按消息数排序"})]}),s.jsxs("select",{value:c,onChange:u=>f(u.target.value),className:"px-4 py-2 rounded-lg text-gray-900",children:[s.jsx("option",{value:"all",children:"全部状态"}),s.jsx("option",{value:"consensus",children:"已达成共识"}),s.jsx("option",{value:"ended",children:"已结束"})]})]})]}),s.jsx("div",{className:"flex-1 overflow-y-auto p-6",children:N.length===0?s.jsxs("div",{className:"text-center py-12",children:[s.jsx(Ss,{size:64,className:"text-gray-400 mx-auto mb-4"}),s.jsx("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"暂无讨论记录"}),s.jsx("p",{className:"text-gray-600",children:"开始一个新的讨论来创建历史记录"})]}):s.jsx("div",{className:"space-y-4",children:N.map(u=>{const p=t.agents.filter(j=>u.participants.includes(j.id));return s.jsx("div",{className:"bg-gray-50 rounded-lg border border-gray-200",children:s.jsxs("div",{className:"p-4",children:[s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{className:"flex-1",children:[s.jsxs("div",{className:"flex items-center gap-3 mb-2",children:[m(u.status),s.jsx("h3",{className:"font-semibold text-gray-900",children:u.topic}),s.jsx("span",{className:"text-sm text-gray-500",children:d(u.status)})]}),s.jsxs("div",{className:"flex items-center gap-6 text-sm text-gray-600",children:[s.jsxs("div",{className:"flex items-center gap-1",children:[s.jsx(Uf,{className:"w-4 h-4"}),new Date(u.createdAt).toLocaleString()]}),s.jsxs("div",{className:"flex items-center gap-1",children:[s.jsx(Ve,{className:"w-4 h-4"}),u.messages.length," 条消息"]}),s.jsxs("div",{className:"flex items-center gap-1",children:[s.jsx(st,{className:"w-4 h-4"}),p.length," 位参与者"]}),u.consensusScore!==void 0&&s.jsxs("div",{className:"flex items-center gap-1",children:[s.jsx(qu,{className:"w-4 h-4"}),"共识度 ",Math.round(u.consensusScore),"%"]})]})]}),s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx("button",{onClick:()=>h(u),className:"p-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-md transition-colors",title:"转到聊天室",children:s.jsx(Hf,{className:"w-4 h-4"})}),s.jsx("button",{onClick:()=>w(u.id),disabled:y===u.id,className:"p-2 text-gray-600 hover:text-red-600 hover:bg-red-50 rounded-md disabled:opacity-50 disabled:cursor-not-allowed transition-colors",title:"删除记录",children:y===u.id?s.jsx("div",{className:"animate-spin w-4 h-4 border-2 border-red-500 border-t-transparent rounded-full"}):s.jsx(gr,{className:"w-4 h-4"})}),s.jsx("button",{onClick:()=>g(x===u.id?null:u.id),className:"p-2 text-gray-600 hover:text-purple-600 hover:bg-purple-50 rounded-md transition-colors",title:"查看详情",children:x===u.id?s.jsx(Vf,{className:"w-4 h-4"}):s.jsx(Ff,{className:"w-4 h-4"})})]})]}),x===u.id&&s.jsxs("div",{className:"mt-4 pt-4 border-t border-gray-200",children:[s.jsxs("div",{className:"mb-4",children:[s.jsx("h4",{className:"font-medium text-gray-900 mb-2",children:"参与者"}),s.jsx("div",{className:"flex flex-wrap gap-2",children:p.map(j=>s.jsxs("div",{className:"flex items-center gap-2 bg-white px-3 py-1 rounded-full border",children:[s.jsx("img",{src:j.avatar,alt:j.name,className:"w-6 h-6 rounded-full object-cover"}),s.jsx("span",{className:"text-sm font-medium",children:j.name})]},j.id))})]}),u.consensus&&s.jsxs("div",{className:"mb-4",children:[s.jsx("h4",{className:"font-medium text-gray-900 mb-2",children:"讨论结论"}),s.jsx("div",{className:"bg-green-50 border border-green-200 rounded-lg p-3",children:s.jsx("p",{className:"text-green-800",children:u.consensus})})]}),s.jsxs("div",{children:[s.jsx("h4",{className:"font-medium text-gray-900 mb-2",children:"消息预览"}),s.jsxs("div",{className:"space-y-2 max-h-60 overflow-y-auto",children:[u.messages.slice(0,5).map(j=>{const S=p.find(T=>T.id===j.agentId);return s.jsxs("div",{className:"bg-white p-3 rounded border",children:[s.jsxs("div",{className:"flex items-center gap-2 mb-1",children:[s.jsx("span",{className:"font-medium text-sm",children:S==null?void 0:S.name}),s.jsx("span",{className:"text-xs text-gray-500",children:new Date(j.timestamp).toLocaleTimeString()})]}),s.jsx("p",{className:"text-sm text-gray-700 line-clamp-2",children:j.content})]},j.id)}),u.messages.length>5&&s.jsxs("div",{className:"text-center text-sm text-gray-500",children:["还有 ",u.messages.length-5," 条消息..."]})]})]})]})]})},u.id)})})})]})})})},Cp=()=>{const{state:e}=Pt(),[t,n]=L.useState(!1),[r,l]=L.useState([{id:"storage",label:"初始化存储服务",status:"pending"},{id:"server",label:"检查服务器连接",status:"pending"},{id:"agents",label:"加载智能体配置",status:"pending"},{id:"llm",label:"加载LLM配置",status:"pending"},{id:"discussions",label:"加载讨论历史",status:"pending"}]);L.useEffect(()=>{l(c=>c.map(f=>{const x=c.findIndex(y=>y.id===f.id),g=c.findIndex(y=>y.id===e.loadingStep);return x<g?{...f,status:"completed"}:x===g?{...f,status:"loading"}:{...f,status:"pending"}}))},[e.loadingStep]),L.useEffect(()=>{const c=setTimeout(()=>{n(!0)},1e4);return()=>clearTimeout(c)},[]);const i=c=>{switch(c){case"completed":return s.jsx(vt,{className:"w-4 h-4 text-green-500"});case"loading":return s.jsx(Na,{className:"w-4 h-4 text-blue-500 animate-spin"});case"error":return s.jsx(En,{className:"w-4 h-4 text-red-500"});default:return s.jsx(Hu,{className:"w-4 h-4 text-gray-400"})}},o=r.filter(c=>c.status==="completed").length,a=o/r.length*100;return s.jsx("div",{className:"min-h-screen bg-gradient-to-br from-indigo-50 via-blue-50 to-purple-50 flex items-center justify-center",children:s.jsxs("div",{className:"text-center max-w-md mx-auto",children:[s.jsxs("div",{className:"flex items-center justify-center gap-3 mb-8",children:[s.jsx(Lt,{size:48,className:"text-blue-600 animate-pulse"}),s.jsx("h1",{className:"text-4xl font-bold text-gray-900",children:"多智能体讨论系统"})]}),s.jsxs("div",{className:"flex items-center justify-center gap-3 mb-6",children:[s.jsx(Na,{className:"w-6 h-6 text-blue-600 animate-spin"}),s.jsx("span",{className:"text-lg text-gray-600",children:"正在初始化系统..."})]}),s.jsxs("div",{className:"w-80 mx-auto mb-6",children:[s.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2",children:s.jsx("div",{className:"bg-blue-500 h-2 rounded-full transition-all duration-500",style:{width:`${a}%`}})}),s.jsxs("div",{className:"mt-2 text-sm text-gray-500",children:[o,"/",r.length," 步骤完成"]})]}),s.jsx("div",{className:"space-y-3 text-sm",children:r.map(c=>s.jsxs("div",{className:"flex items-center justify-center gap-3",children:[i(c.status),s.jsx("span",{className:`${c.status==="completed"?"text-green-600":c.status==="loading"?"text-blue-600":c.status==="error"?"text-red-600":"text-gray-500"}`,children:c.label})]},c.id))}),t&&s.jsxs("div",{className:"mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg",children:[s.jsxs("div",{className:"flex items-center gap-2 text-yellow-800",children:[s.jsx(En,{className:"w-5 h-5"}),s.jsx("span",{className:"font-medium",children:"加载时间较长"})]}),s.jsx("p",{className:"text-sm text-yellow-700 mt-1",children:"连接服务器超时，原因是网络较慢或后台服务不可用。"})]})]})})};class Ep extends L.Component{constructor(n){super(n);Me(this,"handleReload",()=>{window.location.reload()});Me(this,"handleReset",()=>{this.setState({hasError:!1,error:null,errorInfo:null})});this.state={hasError:!1,error:null,errorInfo:null}}static getDerivedStateFromError(n){return{hasError:!0,error:n,errorInfo:null}}componentDidCatch(n,r){console.error("ErrorBoundary caught an error:",n,r),this.setState({error:n,errorInfo:r})}render(){var n,r,l;if(this.state.hasError){const i=(r=(n=this.state.error)==null?void 0:n.message)==null?void 0:r.includes("无法连接到后端服务器");return s.jsx("div",{className:"min-h-screen bg-gradient-to-br from-red-50 via-orange-50 to-yellow-50 flex items-center justify-center",children:s.jsxs("div",{className:"text-center max-w-2xl mx-auto p-8",children:[s.jsxs("div",{className:"flex items-center justify-center gap-3 mb-6",children:[s.jsx(di,{size:48,className:"text-red-500"}),s.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:i?"无法连接后端服务":"系统初始化失败"})]}),s.jsxs("div",{className:"bg-white rounded-lg shadow-lg p-6 mb-6",children:[s.jsx("h2",{className:"text-lg font-semibold text-gray-800 mb-4",children:"错误详情"}),s.jsx("div",{className:"text-left bg-gray-50 rounded p-4 mb-4",children:s.jsx("p",{className:"text-red-600 font-mono text-sm",children:((l=this.state.error)==null?void 0:l.message)||"未知错误"})}),s.jsx("div",{className:"text-sm text-gray-600 mb-4",children:i?s.jsxs(s.Fragment,{children:[s.jsx("p",{className:"font-medium mb-2",children:"请检查以下项目："}),s.jsxs("ul",{className:"list-disc list-inside space-y-1",children:[s.jsx("li",{children:"后端服务是否已启动（端口5000）"}),s.jsx("li",{children:"网络连接是否正常"}),s.jsx("li",{children:"防火墙是否阻止了连接"}),s.jsx("li",{children:"后端服务地址配置是否正确"})]})]}):s.jsxs(s.Fragment,{children:[s.jsx("p",{children:"可能的原因："}),s.jsxs("ul",{className:"list-disc list-inside mt-2 space-y-1",children:[s.jsx("li",{children:"网络连接问题"}),s.jsx("li",{children:"后端服务器不可用"}),s.jsx("li",{children:"浏览器存储空间不足"}),s.jsx("li",{children:"配置文件损坏"})]})]})})]}),s.jsxs("div",{className:"flex gap-4 justify-center",children:[s.jsxs("button",{onClick:this.handleReload,className:"flex items-center gap-2 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[s.jsx(Yf,{size:20}),"重新加载页面"]}),s.jsx("button",{onClick:this.handleReset,className:"flex items-center gap-2 px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors",children:"重试初始化"})]}),i&&s.jsxs("div",{className:"mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg",children:[s.jsx("p",{className:"text-blue-800 font-medium mb-2",children:"启动后端服务："}),s.jsxs("div",{className:"text-sm text-blue-700 space-y-1",children:[s.jsxs("p",{children:["Windows: 运行 ",s.jsx("code",{className:"bg-blue-100 px-1 rounded",children:"start.bat"})]}),s.jsxs("p",{children:["Linux/macOS: 运行 ",s.jsx("code",{className:"bg-blue-100 px-1 rounded",children:"./start.sh"})]})]})]})]})})}return this.props.children}}function Lp(){const{state:e}=Pt(),[t,n]=L.useState("home");if($a.useEffect(()=>{e.isDiscussionActive&&e.currentDiscussion&&n("discussion")},[e.isDiscussionActive,e.currentDiscussion]),e.isLoading)return s.jsx(Cp,{});const r=()=>{switch(t){case"agents":return s.jsx(ap,{});case"llm":return s.jsx(Sp,{});case"data":return s.jsx(kp,{});case"history":return s.jsx(bp,{onNavigate:n});case"setup":return s.jsx(dp,{});case"discussion":return s.jsx(wp,{});default:return s.jsx(Tp,{onNavigate:n})}};return s.jsxs("div",{className:"h-screen bg-gray-50 w-full flex flex-col",children:[t!=="home"&&s.jsx("nav",{className:"flex-shrink-0 bg-white shadow-sm border-b border-gray-200 w-full",children:s.jsx("div",{className:"w-full px-6",children:s.jsxs("div",{className:"flex items-center justify-between h-16",children:[s.jsxs("div",{className:"flex items-center gap-8",children:[s.jsxs("button",{onClick:()=>n("home"),className:"flex items-center gap-2 text-gray-900 hover:text-blue-600 font-medium",children:[s.jsx(Lt,{size:24,className:"text-blue-600"}),"多智能体讨论系统"]}),s.jsxs("div",{className:"flex gap-6",children:[s.jsxs("button",{onClick:()=>n("agents"),className:`flex items-center gap-2 px-3 py-2 rounded-md transition-colors ${t==="agents"?"bg-blue-100 text-blue-700":"text-gray-600 hover:text-gray-900"}`,children:[s.jsx(st,{size:20}),"智能体管理"]}),s.jsxs("button",{onClick:()=>n("llm"),className:`flex items-center gap-2 px-3 py-2 rounded-md transition-colors ${t==="llm"?"bg-orange-100 text-orange-700":"text-gray-600 hover:text-gray-900"}`,children:[s.jsx(Bu,{size:20}),"LLM管理"]}),s.jsxs("button",{onClick:()=>n("history"),className:`flex items-center gap-2 px-3 py-2 rounded-md transition-colors ${t==="history"?"bg-purple-100 text-purple-700":"text-gray-600 hover:text-gray-900"}`,children:[s.jsx(Ss,{size:20}),"讨论历史"]}),s.jsxs("button",{onClick:()=>n("data"),className:`flex items-center gap-2 px-3 py-2 rounded-md transition-colors ${t==="data"?"bg-indigo-100 text-indigo-700":"text-gray-600 hover:text-gray-900"}`,children:[s.jsx(uo,{size:20}),"数据管理"]}),s.jsxs("button",{onClick:()=>n("setup"),className:`flex items-center gap-2 px-3 py-2 rounded-md transition-colors ${t==="setup"?"bg-blue-100 text-blue-700":"text-gray-600 hover:text-gray-900"}`,disabled:e.agents.length===0,children:[s.jsx(Tt,{size:20}),"创建讨论"]}),e.isDiscussionActive&&s.jsxs("button",{onClick:()=>n("discussion"),className:`flex items-center gap-2 px-3 py-2 rounded-md transition-colors ${t==="discussion"?"bg-green-100 text-green-700":"text-green-600 hover:text-green-700"}`,children:[s.jsx(Ve,{size:20}),"讨论进行中",s.jsx("div",{className:"w-2 h-2 bg-green-500 rounded-full animate-pulse"})]})]})]}),s.jsxs("div",{className:"flex items-center gap-4",children:[s.jsxs("span",{className:"text-sm text-gray-500",children:[e.agents.length," 个智能体"]}),e.isDiscussionActive&&s.jsxs("div",{className:"flex items-center gap-2 bg-green-50 text-green-700 px-3 py-1 rounded-full text-sm",children:[s.jsx("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),"讨论中"]})]})]})})}),s.jsx("div",{className:"flex-1 overflow-hidden",children:r()})]})}function Tp({onNavigate:e}){var n;const{state:t}=Pt();return s.jsx("div",{className:"h-full bg-gradient-to-br from-indigo-50 via-blue-50 to-purple-50 w-full fixed-layout overflow-y-auto",children:s.jsxs("div",{className:"w-full px-6 py-12 fixed-layout",children:[s.jsxs("div",{className:"text-center mb-16",children:[s.jsxs("div",{className:"flex items-center justify-center gap-3 mb-6",children:[s.jsx(Lt,{size:48,className:"text-blue-600"}),s.jsx("h1",{className:"text-5xl font-bold text-gray-900",children:"多智能体讨论系统"})]}),s.jsx("p",{className:"text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed",children:"通过配置不同专业背景和思维方式的AI智能体，创建富有洞察力的讨论环境， 探索复杂问题的多维度解决方案，并达成有价值的共识。"}),s.jsxs("div",{className:"flex items-center justify-center gap-8 mt-8 text-sm text-gray-500",children:[s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx(ka,{size:16,className:"text-yellow-500"}),"支持2-8个智能体同时讨论"]}),s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx(Lt,{size:16,className:"text-purple-500"}),"智能共识判断算法"]}),s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx(Ve,{size:16,className:"text-blue-500"}),"实时讨论模拟"]})]})]}),s.jsxs("div",{className:"space-y-8 mb-16",children:[s.jsxs("div",{className:"flex flex-wrap justify-center gap-6",children:[s.jsx("div",{className:"group",children:s.jsxs("div",{className:"bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all p-8 border border-gray-100 group-hover:border-blue-200 fixed-size-card",children:[s.jsxs("div",{className:"flex items-center gap-4 mb-6",children:[s.jsx("div",{className:"w-16 h-16 bg-blue-100 rounded-xl flex items-center justify-center group-hover:bg-blue-200 transition-colors",children:s.jsx(st,{size:32,className:"text-blue-600"})}),s.jsxs("div",{children:[s.jsx("h3",{className:"text-xl font-bold text-gray-900",children:"智能体管理"}),s.jsx("p",{className:"text-gray-500",children:"配置AI智能体"})]})]}),s.jsx("p",{className:"text-gray-600 mb-6 leading-relaxed",children:"创建和配置具有不同专业背景、思维方式和性格特征的智能体。 每个智能体都有独特的知识领域和讨论风格。"}),s.jsxs("div",{className:"mb-6",children:[s.jsxs("div",{className:"flex items-center justify-between text-sm text-gray-500 mb-2",children:[s.jsx("span",{children:"当前智能体数量"}),s.jsxs("span",{className:"font-bold text-blue-600 text-lg",children:[t.agents.length,"/8"]})]}),s.jsx("div",{className:"w-full h-2 bg-gray-200 rounded-full",children:s.jsx("div",{className:"h-full bg-blue-500 rounded-full transition-all",style:{width:`${t.agents.length/8*100}%`}})})]}),s.jsx("button",{onClick:()=>e("agents"),className:"w-full bg-blue-600 text-white py-3 rounded-xl hover:bg-blue-700 transition-colors font-medium",children:"管理智能体"})]})}),s.jsx("div",{className:"group",children:s.jsxs("div",{className:"bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all p-8 border border-gray-100 group-hover:border-orange-200 fixed-size-card",children:[s.jsxs("div",{className:"flex items-center gap-4 mb-6",children:[s.jsx("div",{className:"w-16 h-16 bg-orange-100 rounded-xl flex items-center justify-center group-hover:bg-orange-200 transition-colors",children:s.jsx(Bu,{size:32,className:"text-orange-600"})}),s.jsxs("div",{children:[s.jsx("h3",{className:"text-xl font-bold text-gray-900",children:"LLM管理"}),s.jsx("p",{className:"text-gray-500",children:"配置大语言模型"})]})]}),s.jsx("p",{className:"text-gray-600 mb-6 leading-relaxed",children:"配置和管理大语言模型，为智能体提供真实的AI对话能力。 支持OpenAI、Anthropic等多种提供商。"}),s.jsxs("div",{className:"space-y-3 mb-6",children:[s.jsxs("div",{className:"flex items-center gap-2 text-sm text-gray-600",children:[s.jsx("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),"支持多种LLM提供商"]}),s.jsxs("div",{className:"flex items-center gap-2 text-sm text-gray-600",children:[s.jsx("div",{className:"w-2 h-2 bg-blue-500 rounded-full"}),"个性化配置参数"]}),s.jsxs("div",{className:"flex items-center gap-2 text-sm text-gray-600",children:[s.jsx("div",{className:"w-2 h-2 bg-purple-500 rounded-full"}),"连接测试功能"]})]}),s.jsx("button",{onClick:()=>e("llm"),className:"w-full bg-orange-600 text-white py-3 rounded-xl hover:bg-orange-700 transition-colors font-medium",children:"管理LLM配置"})]})}),s.jsx("div",{className:"group",children:s.jsxs("div",{className:"bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all p-8 border border-gray-100 group-hover:border-indigo-200 fixed-size-card",children:[s.jsxs("div",{className:"flex items-center gap-4 mb-6",children:[s.jsx("div",{className:"w-16 h-16 bg-indigo-100 rounded-xl flex items-center justify-center group-hover:bg-indigo-200 transition-colors",children:s.jsx(uo,{size:32,className:"text-indigo-600"})}),s.jsxs("div",{children:[s.jsx("h3",{className:"text-xl font-bold text-gray-900",children:"数据管理"}),s.jsx("p",{className:"text-gray-500",children:"备份与恢复"})]})]}),s.jsx("p",{className:"text-gray-600 mb-6 leading-relaxed",children:"管理系统数据的备份、恢复和清理，确保配置和历史记录的安全。"}),s.jsxs("div",{className:"space-y-3 mb-6",children:[s.jsxs("div",{className:"flex items-center gap-2 text-sm text-gray-600",children:[s.jsx("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),"数据导出备份"]}),s.jsxs("div",{className:"flex items-center gap-2 text-sm text-gray-600",children:[s.jsx("div",{className:"w-2 h-2 bg-blue-500 rounded-full"}),"配置导入恢复"]}),s.jsxs("div",{className:"flex items-center gap-2 text-sm text-gray-600",children:[s.jsx("div",{className:"w-2 h-2 bg-red-500 rounded-full"}),"数据清理重置"]})]}),s.jsx("button",{onClick:()=>e("data"),className:"w-full bg-indigo-600 text-white py-3 rounded-xl hover:bg-indigo-700 transition-colors font-medium",children:"管理数据"})]})})]}),s.jsxs("div",{className:"flex flex-wrap justify-center gap-6",children:[s.jsx("div",{className:"group",children:s.jsxs("div",{className:"bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all p-8 border border-gray-100 group-hover:border-purple-200 fixed-size-card",children:[s.jsxs("div",{className:"flex items-center gap-4 mb-6",children:[s.jsx("div",{className:"w-16 h-16 bg-purple-100 rounded-xl flex items-center justify-center group-hover:bg-purple-200 transition-colors",children:s.jsx(Tt,{size:32,className:"text-purple-600"})}),s.jsxs("div",{children:[s.jsx("h3",{className:"text-xl font-bold text-gray-900",children:"创建讨论"}),s.jsx("p",{className:"text-gray-500",children:"配置讨论参数"})]})]}),s.jsx("p",{className:"text-gray-600 mb-6 leading-relaxed",children:"设置讨论话题、选择参与的智能体、配置讨论模式， 开始一场富有见解的AI讨论。"}),s.jsxs("div",{className:"space-y-3 mb-6",children:[s.jsxs("div",{className:"flex items-center gap-2 text-sm text-gray-600",children:[s.jsx("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),"自由讨论模式"]}),s.jsxs("div",{className:"flex items-center gap-2 text-sm text-gray-600",children:[s.jsx("div",{className:"w-2 h-2 bg-blue-500 rounded-full"}),"主持人模式"]}),s.jsxs("div",{className:"flex items-center gap-2 text-sm text-gray-600",children:[s.jsx("div",{className:"w-2 h-2 bg-orange-500 rounded-full"}),"智能共识判断"]})]}),s.jsx("button",{onClick:()=>e("setup"),disabled:t.agents.length<2,className:"w-full bg-purple-600 text-white py-3 rounded-xl hover:bg-purple-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors font-medium",children:t.agents.length<2?"需要至少2个智能体":"创建新讨论"})]})}),s.jsx("div",{className:"group",children:s.jsxs("div",{className:"bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all p-8 border border-gray-100 group-hover:border-green-200 fixed-size-card",children:[s.jsxs("div",{className:"flex items-center gap-4 mb-6",children:[s.jsx("div",{className:"w-16 h-16 bg-green-100 rounded-xl flex items-center justify-center group-hover:bg-green-200 transition-colors",children:s.jsx(Ve,{size:32,className:"text-green-600"})}),s.jsxs("div",{children:[s.jsx("h3",{className:"text-xl font-bold text-gray-900",children:"讨论状态"}),s.jsx("p",{className:"text-gray-500",children:"实时监控"})]})]}),t.isDiscussionActive?s.jsxs(s.Fragment,{children:[s.jsx("p",{className:"text-gray-600 mb-6 leading-relaxed",children:"当前有一场讨论正在进行中，您可以实时观看智能体之间的对话， 监控共识度变化。"}),s.jsxs("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4 mb-6",children:[s.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[s.jsx("div",{className:"w-2 h-2 bg-green-500 rounded-full animate-pulse"}),s.jsx("span",{className:"font-medium text-green-800",children:"讨论进行中"})]}),s.jsxs("p",{className:"text-green-700 text-sm",children:["话题：",(n=t.currentDiscussion)==null?void 0:n.topic]})]}),s.jsx("button",{onClick:()=>e("discussion"),className:"w-full bg-green-600 text-white py-3 rounded-xl hover:bg-green-700 transition-colors font-medium",children:"进入讨论室"})]}):s.jsxs(s.Fragment,{children:[s.jsx("p",{className:"text-gray-600 mb-6 leading-relaxed",children:"目前没有进行中的讨论。创建智能体并配置讨论参数后， 您就可以开始一场精彩的AI对话。"}),s.jsxs("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-4 mb-6",children:[s.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[s.jsx("div",{className:"w-2 h-2 bg-gray-400 rounded-full"}),s.jsx("span",{className:"font-medium text-gray-600",children:"空闲状态"})]}),s.jsxs("p",{className:"text-gray-500 text-sm",children:["历史讨论：",t.allDiscussions.length," 场"]})]}),s.jsx("button",{onClick:()=>e(t.agents.length<2?"agents":"setup"),className:"w-full bg-gray-400 text-white py-3 rounded-xl hover:bg-gray-500 transition-colors font-medium",children:t.agents.length<2?"先创建智能体":"开始新讨论"})]})]})}),s.jsx("div",{className:"group",children:s.jsxs("div",{className:"bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all p-8 border border-gray-100 group-hover:border-purple-200 fixed-size-card",children:[s.jsxs("div",{className:"flex items-center gap-4 mb-6",children:[s.jsx("div",{className:"w-16 h-16 bg-purple-100 rounded-xl flex items-center justify-center group-hover:bg-purple-200 transition-colors",children:s.jsx(Ss,{size:32,className:"text-purple-600"})}),s.jsxs("div",{children:[s.jsx("h3",{className:"text-xl font-bold text-gray-900",children:"讨论历史"}),s.jsx("p",{className:"text-gray-500",children:"查看历史记录"})]})]}),s.jsx("p",{className:"text-gray-600 mb-6 leading-relaxed",children:"查看和分析历史讨论记录，了解智能体的对话模式和共识形成过程。"}),s.jsxs("div",{className:"mb-6",children:[s.jsxs("div",{className:"flex items-center justify-between text-sm text-gray-500 mb-2",children:[s.jsx("span",{children:"历史讨论数量"}),s.jsx("span",{className:"font-bold text-purple-600 text-lg",children:t.allDiscussions.length})]}),s.jsx("div",{className:"w-full h-2 bg-gray-200 rounded-full",children:s.jsx("div",{className:"h-full bg-purple-500 rounded-full transition-all",style:{width:`${Math.min(t.allDiscussions.length/10*100,100)}%`}})})]}),s.jsx("button",{onClick:()=>e("history"),className:"w-full bg-purple-600 text-white py-3 rounded-xl hover:bg-purple-700 transition-colors font-medium",children:"查看历史"})]})})]})]}),s.jsxs("div",{className:"bg-white rounded-2xl shadow-xl p-12 border border-gray-100",children:[s.jsx("h2",{className:"text-3xl font-bold text-gray-900 text-center mb-8",children:"系统特色功能"}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8",children:[s.jsxs("div",{className:"text-center",children:[s.jsx("div",{className:"w-16 h-16 bg-blue-100 rounded-xl flex items-center justify-center mx-auto mb-4",children:s.jsx(Lt,{size:32,className:"text-blue-600"})}),s.jsx("h3",{className:"font-semibold text-gray-900 mb-2",children:"智能化对话"}),s.jsx("p",{className:"text-gray-600 text-sm",children:"基于专业领域和性格特征生成真实的对话内容"})]}),s.jsxs("div",{className:"text-center",children:[s.jsx("div",{className:"w-16 h-16 bg-green-100 rounded-xl flex items-center justify-center mx-auto mb-4",children:s.jsx(ka,{size:32,className:"text-green-600"})}),s.jsx("h3",{className:"font-semibold text-gray-900 mb-2",children:"实时共识监控"}),s.jsx("p",{className:"text-gray-600 text-sm",children:"动态计算讨论共识度，智能判断达成一致的时机"})]}),s.jsxs("div",{className:"text-center",children:[s.jsx("div",{className:"w-16 h-16 bg-purple-100 rounded-xl flex items-center justify-center mx-auto mb-4",children:s.jsx(st,{size:32,className:"text-purple-600"})}),s.jsx("h3",{className:"font-semibold text-gray-900 mb-2",children:"多模式讨论"}),s.jsx("p",{className:"text-gray-600 text-sm",children:"支持自由讨论和主持人模式，适应不同讨论需求"})]}),s.jsxs("div",{className:"text-center",children:[s.jsx("div",{className:"w-16 h-16 bg-orange-100 rounded-xl flex items-center justify-center mx-auto mb-4",children:s.jsx(Ve,{size:32,className:"text-orange-600"})}),s.jsx("h3",{className:"font-semibold text-gray-900 mb-2",children:"丰富的交互"}),s.jsx("p",{className:"text-gray-600 text-sm",children:"支持多种消息类型，包括陈述、提问、同意、反对"})]})]})]})]})})}function Dp(){return s.jsx(Ep,{children:s.jsx(zf,{children:s.jsx(Lp,{})})})}gl.createRoot(document.getElementById("root")).render(s.jsx($a.StrictMode,{children:s.jsx(Dp,{})}));
